  "use client";

import { useEffect, useState } from 'react';

import MatchCard from '@/components/common/MatchCard';
import { MatchData } from '@/types/match';
import { fetchMatches } from '@/services/matchService';
import { useAutoMatchOrderMapper } from '@/hooks/useMatchOrderMapper';

export default function MatchOrderDemo() {
  const [matches, setMatches] = useState<MatchData[]>([]);
  const [matchesLoading, setMatchesLoading] = useState(true);

  // Configuration cho Google Sheets của bạn
  const SPREADSHEET_ID = '1Xw1IpIyjMtuIfbWlJ9kyiwioUoYMgRCydayKBI93j3Y'; // Thay bằng ID thực của bạn
  const SHEET_NAME = 'Sheet1'; // Tên sheet của bạn
  const RANGE = 'A1:G'; // Range A-G như trong hình

  // Step 1: Fetch matches từ API
  useEffect(() => {
    const loadMatches = async () => {
      try {
        setMatchesLoading(true);
        console.log('🔄 Fetching matches from fetchMatches API...');
        
        const result = await fetchMatches({
          category: 'football',
          limit: 20,
          sortBy: 'status,time,date',
          sortOrder: 'DESC,ASC,ASC'
        });
        
        console.log('✅ Fetched matches:', result.data.length);
        setMatches(result.data);
      } catch (error) {
        console.error('❌ Error fetching matches:', error);
      } finally {
        setMatchesLoading(false);
      }
    };

    loadMatches();
  }, []);

  // Step 2: Auto map với Google Sheets theo cấu trúc order/id
  const {
    mappedMatches,
    loading: mappingLoading,
    error: mappingError,
    stats
  } = useAutoMatchOrderMapper(matches, SPREADSHEET_ID, SHEET_NAME, RANGE);

  // Loading state
  if (matchesLoading) {
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">Loading matches...</h2>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6">Match Order Demo</h2>
      <p className="text-gray-600 mb-6">
        Demo map dữ liệu từ fetchMatches API với Google Sheets (order/id) để hiển thị viền đỏ
      </p>
      
      {/* Status và Statistics */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-3">Mapping Status</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="bg-white p-3 rounded">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-gray-600">Total Matches</div>
          </div>
          <div className="bg-white p-3 rounded">
            <div className="text-2xl font-bold text-green-600">{stats.inSheet}</div>
            <div className="text-gray-600">In Google Sheets</div>
          </div>
          <div className="bg-white p-3 rounded">
            <div className="text-2xl font-bold text-red-600">{stats.withRedBorder}</div>
            <div className="text-gray-600">Red Border</div>
          </div>
          <div className="bg-white p-3 rounded">
            <div className="text-2xl font-bold text-orange-600">{stats.liveMatches}</div>
            <div className="text-gray-600">Live Matches</div>
          </div>
        </div>
        
        {mappingLoading && (
          <div className="mt-3 text-blue-600">🔄 Mapping with Google Sheets...</div>
        )}
        
        {mappingError && (
          <div className="mt-3 text-red-600">❌ Mapping error: {mappingError}</div>
        )}
      </div>

      {/* Legend */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-3">Border Logic</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center gap-3 p-3 bg-red-50 rounded border border-red-200">
            <div className="w-6 h-6 border-2 border-red-500 rounded"></div>
            <div>
              <div className="font-medium text-red-800">Red Border</div>
              <div className="text-red-600">Live + có Order trong Sheets</div>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-orange-50 rounded border border-orange-200">
            <div className="w-6 h-6 border-2 border-orange-500 rounded"></div>
            <div>
              <div className="font-medium text-orange-800">Orange Border</div>
              <div className="text-orange-600">Live + không có Order</div>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded border border-gray-200">
            <div className="w-6 h-6 border border-gray-400 rounded"></div>
            <div>
              <div className="font-medium text-gray-800">Normal Border</div>
              <div className="text-gray-600">Không Live</div>
            </div>
          </div>
        </div>
      </div>

      {/* Matches Display */}
      <div className="space-y-6">
        {/* Red Border Matches */}
        {stats.withRedBorder > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-3 text-red-600">
              🔴 Matches với Red Border ({stats.withRedBorder})
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mappedMatches
                .filter(match => match.orderInfo?.hasRedBorder)
                .map((match, index) => (
                  <div key={match.id || index} className="relative">
                    <MatchCard
                      match={match}
                      variant="default"
                      onClick={() => {
                        console.log('Red border match clicked:', {
                          id: match.id,
                          status: match.status,
                          order: match.orderInfo?.order,
                          hasRedBorder: match.orderInfo?.hasRedBorder
                        });
                      }}
                    />
                    
                    {/* Order Info Badge */}
                    <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                      Order: {match.orderInfo?.order}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Live Matches without Order */}
        <div>
          <h3 className="text-lg font-semibold mb-3 text-orange-600">
            🟠 Live Matches không có Order ({mappedMatches.filter(m => m.status === 'live' && !m.orderInfo?.hasRedBorder).length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mappedMatches
              .filter(match => match.status === 'live' && !match.orderInfo?.hasRedBorder)
              .map((match, index) => (
                <div key={match.id || index} className="relative">
                  <MatchCard
                    match={match}
                    variant="default"
                    onClick={() => {
                      console.log('Orange border match clicked:', {
                        id: match.id,
                        status: match.status,
                        inSheet: match.orderInfo?.isFromSheet
                      });
                    }}
                  />
                  
                  {/* Status Badge */}
                  <div className="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded">
                    Live
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Normal Matches */}
        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-600">
            ⚪ Normal Matches ({mappedMatches.filter(m => m.status !== 'live').length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mappedMatches
              .filter(match => match.status !== 'live')
              .slice(0, 6) // Chỉ hiển thị 6 matches đầu
              .map((match, index) => (
                <div key={match.id || index} className="relative">
                  <MatchCard
                    match={match}
                    variant="default"
                    onClick={() => {
                      console.log('Normal match clicked:', {
                        id: match.id,
                        status: match.status,
                        inSheet: match.orderInfo?.isFromSheet
                      });
                    }}
                  />
                  
                  {/* Sheet Status Badge */}
                  {match.orderInfo?.isFromSheet && (
                    <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                      In Sheet
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* No matches */}
      {mappedMatches.length === 0 && !matchesLoading && !mappingLoading && (
        <div className="text-center py-8 text-gray-500">
          No matches found
        </div>
      )}

      {/* Debug Information */}
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Debug Information</h3>
        <div className="text-sm space-y-1 font-mono">
          <div>Spreadsheet ID: {SPREADSHEET_ID}</div>
          <div>Sheet Name: {SHEET_NAME}</div>
          <div>Range: {RANGE}</div>
          <div>Matches loaded: {matches.length}</div>
          <div>Mapping status: {mappingLoading ? 'Loading...' : 'Complete'}</div>
          {mappingError && <div className="text-red-600">Error: {mappingError}</div>}
        </div>
        
        {/* Sample data */}
        {mappedMatches.length > 0 && (
          <details className="mt-4">
            <summary className="cursor-pointer font-medium">Sample Mapped Data</summary>
            <pre className="mt-2 text-xs bg-white p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(mappedMatches.find(m => m.orderInfo?.hasRedBorder) || mappedMatches[0], null, 2)}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}
