import { useState, useCallback, useEffect } from 'react';
import { MatchData } from '@/types/match';
import {
  MatchWithOrderInfo,
  GoogleSheetRowData,
  fetchAndMapMatchesWithOrder,
  mapMatchesWithOrderData,
  fetchOrderSheetData
} from '@/utils/matchOrderMapper';

interface UseMatchOrderMapperReturn {
  mappedMatches: MatchWithOrderInfo[];
  loading: boolean;
  error: string | null;
  mapMatches: (matches: MatchData[], spreadsheetId: string, sheetName?: string, range?: string) => Promise<void>;
  clearData: () => void;
  stats: {
    total: number;
    inSheet: number;
    withRedBorder: number;
    liveMatches: number;
  };
}

/**
 * Hook để map matches với Google Sheets theo cấu trúc order/id của bạn
 */
export function useMatchOrderMapper(): UseMatchOrderMapperReturn {
  const [mappedMatches, setMappedMatches] = useState<MatchWithOrderInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mapMatches = useCallback(async (
    matches: MatchData[], 
    spreadsheetId: string, 
    sheetName: string = 'Sheet1',
    range: string = 'A1:G'
  ) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🚀 Starting match mapping with order data...');
      console.log(`📊 Input: ${matches.length} matches`);
      console.log(`📋 Sheet: ${sheetName}, Range: ${range}`);

      const mappedData = await fetchAndMapMatchesWithOrder(matches, spreadsheetId, sheetName, range);
      setMappedMatches(mappedData);

      console.log('✅ Match mapping completed successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi map dữ liệu';
      setError(errorMessage);
      console.error('❌ Error mapping matches:', err);
      
      // Fallback: return original matches without order data
      const fallbackData: MatchWithOrderInfo[] = matches.map(match => ({
        ...match,
        orderInfo: {
          order: '',
          hasRedBorder: false,
          isFromSheet: false
        }
      }));
      setMappedMatches(fallbackData);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearData = useCallback(() => {
    setMappedMatches([]);
    setError(null);
  }, []);

  // Calculate stats
  const stats = {
    total: mappedMatches.length,
    inSheet: mappedMatches.filter(m => m.orderInfo?.isFromSheet).length,
    withRedBorder: mappedMatches.filter(m => m.orderInfo?.hasRedBorder).length,
    liveMatches: mappedMatches.filter(m => m.status === 'live').length
  };

  return {
    mappedMatches,
    loading,
    error,
    mapMatches,
    clearData,
    stats
  };
}

/**
 * Hook để tự động map khi matches thay đổi
 */
export function useAutoMatchOrderMapper(
  matches: MatchData[], 
  spreadsheetId: string, 
  sheetName: string = 'Sheet1',
  range: string = 'A1:G',
  enabled: boolean = true
): UseMatchOrderMapperReturn {
  const {
    mappedMatches,
    loading,
    error,
    mapMatches,
    clearData,
    stats
  } = useMatchOrderMapper();

  useEffect(() => {
    if (enabled && matches.length > 0 && spreadsheetId) {
      mapMatches(matches, spreadsheetId, sheetName, range);
    }
  }, [matches, spreadsheetId, sheetName, range, enabled, mapMatches]);

  return {
    mappedMatches,
    loading,
    error,
    mapMatches,
    clearData,
    stats
  };
}

/**
 * Hook để chỉ fetch Google Sheets data
 */
export function useOrderSheetData(
  spreadsheetId: string,
  sheetName: string = 'Sheet1',
  range: string = 'A1:G'
) {
  const [data, setData] = useState<GoogleSheetRowData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const sheetData = await fetchOrderSheetData(spreadsheetId, sheetName, range);
      setData(sheetData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [spreadsheetId, sheetName, range]);

  useEffect(() => {
    if (spreadsheetId) {
      fetchData();
    }
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}

// Export types
export type { MatchWithOrderInfo, GoogleSheetRowData };
