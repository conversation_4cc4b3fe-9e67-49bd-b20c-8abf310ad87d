"use client";

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import {
  ChatMessage as ChatMessageType
} from '@/services/chatService';
import type { ChatMessage as ChatMessageApi } from '@/types/chat.types';
import { useAuth } from '@/contexts/AuthContext';
import { useSupabase } from '@/contexts/SupabaseContext';
import { ChatService } from '@/services/chat.service';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

interface DirectChatTabProps {
  isLoggedIn: boolean;
  onOpenAuthModal: (mode: 'login' | 'register') => void;
  matchId?: string;
  selectedRoomId?: string | null;
}

export default function DirectChatTab({ isLoggedIn, onOpenAuthModal, matchId = 'default', selectedRoomId }: DirectChatTabProps) {
  const { user, chatRooms, chatRoomsLoading, loadChatRooms, reloadChatRooms, sendMessage } = useAuth();
  const { supabase } = useSupabase();
  
  // Separate messages state for direct chat to avoid conflicts with general chat
  const [directMessages, setDirectMessages] = useState<ChatMessageApi[]>([]);
  const [directMessagesLoading, setDirectMessagesLoading] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [canLoadMore, setCanLoadMore] = useState(true);
  const [replyTo, setReplyTo] = useState<ChatMessageType | null>(null);
  const [selectedDirectRoom, setSelectedDirectRoom] = useState<string | null>(null);
  const [activeSubTab, setActiveSubTab] = useState<'chat' | 'betting'>('chat');

  // Auto-select room when selectedRoomId is provided
  useEffect(() => {
    if (selectedRoomId && selectedRoomId !== selectedDirectRoom) {
      console.log('Setting selectedDirectRoom from selectedRoomId:', selectedRoomId);
      setSelectedDirectRoom(selectedRoomId);
    }
  }, [selectedRoomId, selectedDirectRoom]);

  // Mobile viewport handling
  const [viewportHeight, setViewportHeight] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isIOS, setIsIOS] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const lastLoadedRoomRef = useRef<string | null>(null);
  const subscriptionRef = useRef<(() => void) | null>(null);

  // Filter chat rooms for direct chat only
  const filteredChatRooms = chatRooms.filter(room => room.type === 'direct');

  // Get user data from context or localStorage as fallback
  const getUserData = useCallback(() => {
    // First try to get from context
    if (user) {
      return {
        userId: user.id,
        name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
        email: user.email,
        verified: user.email_confirmed_at ? true : false,
        isAdmin: false // You can add admin logic here
      };
    }

    // Fallback to localStorage for backward compatibility
    const storedUser = localStorage.getItem('userData');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch (error) {
        // Error parsing user data
      }
    }
    return null;
  }, [user]);

  // Handle viewport and mobile detection
  const updateViewportInfo = useCallback(() => {
    const vh = window.innerHeight;
    const vw = window.innerWidth;
    setViewportHeight(vh);
    setIsMobile(vw < 1024);

    // Detect iOS
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    setIsIOS(isIOSDevice);

    // Detect keyboard height on mobile
    if (vw < 1024) {
      if (isIOSDevice) {
        // iOS: Use visual viewport to detect keyboard
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          const keyboardHeight = Math.max(0, window.innerHeight - visualViewport.height);
          setKeyboardHeight(keyboardHeight);
          setIsKeyboardOpen(keyboardHeight > 0);
        }
      } else {
        // Android: Use window height difference
        const initialHeight = window.visualViewport?.height || vh;
        const currentHeight = window.innerHeight;
        const keyboardHeight = Math.max(0, initialHeight - currentHeight);
        setKeyboardHeight(keyboardHeight);
        setIsKeyboardOpen(keyboardHeight > 0);
      }
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    // Chỉ scroll phần chat, không ảnh hưởng trang chính
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, []);

  // Auto-select first direct chat room when rooms are loaded
  useEffect(() => {
    if (filteredChatRooms.length > 0 && !selectedDirectRoom) {
      const firstDirectRoom = filteredChatRooms[0];
      setSelectedDirectRoom(firstDirectRoom.id);
    }
  }, [filteredChatRooms, selectedDirectRoom]);

  // Load messages for direct chat room
  const loadDirectMessages = useCallback(async (roomId: string) => {
    if (!isLoggedIn) return;
    
    // Avoid loading the same room multiple times
    if (lastLoadedRoomRef.current === roomId) {
      console.log('Room already loaded, skipping:', roomId);
      return;
    }
    
    // Avoid loading if already loading
    if (directMessagesLoading) {
      console.log('Already loading messages, skipping:', roomId);
      return;
    }
    
    try {
      console.log('Loading direct messages for room:', roomId);
      setDirectMessagesLoading(true);
      lastLoadedRoomRef.current = roomId;
      
      const chatService = new ChatService(supabase);
      const { messages, error } = await chatService.getMessages(roomId);
      if (error) {
        console.error('Error loading direct messages:', error);
        lastLoadedRoomRef.current = null; // Reset on error
        return;
      }
      console.log('Loaded direct messages:', messages.length, 'messages');
      setDirectMessages(messages);

      // Subscribe to real-time messages for this room
      // Unsubscribe from previous room if exists
      if (subscriptionRef.current) {
        subscriptionRef.current();
      }

      // Subscribe to new room
      const unsubscribe = chatService.subscribeToMessages(
        roomId,
        (newMessage: ChatMessageApi) => {
          console.log('New realtime message received:', newMessage);
          setDirectMessages((prev) => [...prev, newMessage]);
        }
      );

      subscriptionRef.current = unsubscribe;
    } catch (error) {
      console.error('Error loading direct messages:', error);
      lastLoadedRoomRef.current = null; // Reset on error
    } finally {
      setDirectMessagesLoading(false);
    }
  }, [isLoggedIn, supabase, directMessagesLoading]);

  // Load messages when selectedDirectRoom changes for direct chat
  useEffect(() => {
    if (selectedDirectRoom && isLoggedIn) {
      console.log('SelectedDirectRoom changed:', selectedDirectRoom);
      console.log('Available rooms:', filteredChatRooms.map(r => r.id));
      
      // Only load if the room exists in filteredChatRooms
      const roomExists = filteredChatRooms.some(room => room.id === selectedDirectRoom);
      if (roomExists) {
        loadDirectMessages(selectedDirectRoom);
      } else {
        console.log('Room not found in filteredChatRooms, waiting...');
        // Retry after a short delay in case chatRooms are still loading
        const retryTimer = setTimeout(() => {
          const roomExistsRetry = filteredChatRooms.some(room => room.id === selectedDirectRoom);
          if (roomExistsRetry) {
            console.log('Room found on retry, loading messages');
            loadDirectMessages(selectedDirectRoom);
          } else {
            console.log('Room still not found after retry');
          }
        }, 500);
        
        return () => clearTimeout(retryTimer);
      }
    }
  }, [selectedDirectRoom, isLoggedIn, loadDirectMessages, filteredChatRooms]);

  // Initialize viewport info
  useEffect(() => {
    updateViewportInfo();
    window.addEventListener('resize', updateViewportInfo);
    window.addEventListener('orientationchange', updateViewportInfo);

    return () => {
      window.removeEventListener('resize', updateViewportInfo);
      window.removeEventListener('orientationchange', updateViewportInfo);
    };
  }, [updateViewportInfo]);

  // iOS: Handle keyboard events
  useEffect(() => {
    if (isIOS) {
      const handleIOSKeyboardShow = () => {
        setIsKeyboardOpen(true);
        setTimeout(updateViewportInfo, 100);
      };

      const handleIOSKeyboardHide = () => {
        setIsKeyboardOpen(false);
        setTimeout(updateViewportInfo, 100);
      };

      window.addEventListener('focusin', handleIOSKeyboardShow);
      window.addEventListener('focusout', handleIOSKeyboardHide);

      return () => {
        window.removeEventListener('focusin', handleIOSKeyboardShow);
        window.removeEventListener('focusout', handleIOSKeyboardHide);
      };
    }
  }, [isIOS, updateViewportInfo]);

  // Cleanup subscription on unmount
  useEffect(() => {
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current();
      }
    };
  }, []);

  // Merge real messages and auto messages, then sort by timestamp
  const allMessages = React.useMemo(() => {
    // Convert real messages from API format to ChatMessageType format
    const realMessagesConverted = directMessages.map((msg: ChatMessageApi) => {
      const userData = getUserData();
      const isOwnMessage = userData?.userId === msg.user_id;

      return {
        id: msg.id,
        message: msg.content,
        timestamp: new Date(msg.created_at).getTime(),
        userId: msg.user_id,
        userName: isOwnMessage ? 'Bạn' : (msg.user?.full_name || 'Unknown User'),
        userAvatar: msg.user?.avatar_url || '',
        userColor: 'bg-blue-500',
        verified: false,
        isAdmin: false,
        replyTo: null,
        reactions: {} as { [key: string]: number },
        pinned: false,
        isAutoMessage: false
      } as ChatMessageType;
    });

    // For direct chat, don't include auto messages
    return realMessagesConverted.sort((a, b) => a.timestamp - b.timestamp);
  }, [directMessages, getUserData]);

  // Auto-scroll to bottom when new messages arrive (chỉ scroll phần chat)
  useEffect(() => {
    if (allMessages.length > 0) {
      const timer = setTimeout(() => {
        // Chỉ scroll phần chat, không ảnh hưởng trang chính
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [allMessages.length]);

  // iOS: Scroll to bottom when keyboard opens
  useEffect(() => {
    if (isIOS && isKeyboardOpen && chatContainerRef.current) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isIOS, isKeyboardOpen]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const isScrolledUp = scrollTop < scrollHeight - clientHeight - 100;
      setShowScrollToBottom(isScrolledUp);

      // Check if we can load more messages
      const currentMessages = directMessages;
      if (scrollTop < 100 && canLoadMore && !isLoadingMore && currentMessages.length > 0) {
        // loadOlderMessages();
      }
    }
  }, [canLoadMore, isLoadingMore, directMessages.length]);

  const handleSubmit = useCallback(async (messageText: string, replyToMessage?: ChatMessageType) => {
    if (!messageText.trim() || !isLoggedIn) return;

    const userData = getUserData();
    if (!userData) {
      onOpenAuthModal('login');
      return;
    }

    // Check if chat rooms are still loading
    if (chatRoomsLoading) {
      return;
    }

    // Check if we have any chat rooms
    if (chatRooms.length === 0) {
      await reloadChatRooms();
      return;
    }

    // For direct chat, use selected room
    const currentRoom = filteredChatRooms.find(room => room.id === selectedDirectRoom);

    if (!currentRoom) {
      // No room available to send message
      return;
    }

    try {
      const { error } = await sendMessage(currentRoom.id, messageText);
      if (error) {
        // Failed to send message
      } else {
        // Clear reply state after successful send
        setReplyTo(null);
      }
    } catch (error) {
      // Error sending message
    }
  }, [isLoggedIn, getUserData, onOpenAuthModal, chatRooms, chatRoomsLoading, reloadChatRooms, sendMessage, filteredChatRooms, selectedDirectRoom]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // This will be handled by ChatInput component
    }
  }, []);

  const renderMessage = useCallback((message: ChatMessageType, index: number) => {
    const userData = getUserData();
    const isOwnMessage = userData?.userId === message.userId;

    return (
      <ChatMessage
        key={message.id}
        message={message}
        isOwnMessage={isOwnMessage}
        onReply={setReplyTo}
        onReact={() => {}}
        onPin={() => {}}
        onDelete={() => {}}
        isAdmin={false}
      />
    );
  }, [getUserData]);

  // Handle room selection for direct chat
  const handleRoomSelect = (roomId: string) => {
    setSelectedDirectRoom(roomId);
    lastLoadedRoomRef.current = null; // Reset to allow loading new room
    loadDirectMessages(roomId);
  };

  const getChatContainerStyle = () => {
    if (isMobile) {
      return {
        height: `calc(100vh - ${keyboardHeight}px - 200px)`,
        maxHeight: '65vh',
        minHeight: '300px',
      };
    }
    return {};
  };

  const getChatInputStyle = () => {
    if (isMobile) {
      return {
        paddingBottom: isKeyboardOpen ? '0px' : 'env(safe-area-inset-bottom, 0px)',
      };
    }
    return {};
  };

  return (
    <div
      className="flex flex-col h-full relative"
      style={{
        paddingBottom: isMobile ? '0px' : 'env(safe-area-inset-bottom, 0px)',
        // Mobile: Ensure proper height and allow scrolling
        ...(isMobile ? {
          height: '100%',
          overflow: 'hidden',
        } : {})
      }}
    >
      {/* Sub Tab Navigation */}
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setActiveSubTab('chat')}
          className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${activeSubTab === 'chat'
              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
            }`}
        >
          Chat riêng
        </button>
        <button
          onClick={() => setActiveSubTab('betting')}
          className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${activeSubTab === 'betting'
              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
            }`}
        >
          Soi kèo
        </button>
      </div>

      {/* Avatar List for Direct Chat */}
      {activeSubTab === 'chat' && filteredChatRooms.length > 0 && (
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex gap-3 sm:gap-4 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent min-w-0">
            {filteredChatRooms.map((room) => {
              const otherUser = room.other_user;
              const isSelected = selectedDirectRoom === room.id;

              return (
                <div
                  key={room.id}
                  onClick={() => handleRoomSelect(room.id)}
                  className={`flex-shrink-0 flex flex-col items-center cursor-pointer transition-all duration-200 min-w-0 ${isSelected
                      ? 'transform scale-105'
                      : 'hover:transform hover:scale-105'
                    }`}
                >
                  <div className={`relative w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden border-2 transition-colors ${isSelected
                      ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
                    }`}>
                    {otherUser?.avatar_url ? (
                      <img
                        src={otherUser.avatar_url}
                        alt={otherUser.full_name || 'User'}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                        {(otherUser?.full_name || room.name || 'U').charAt(0).toUpperCase()}
                      </div>
                    )}
                    {/* Online indicator */}
                    <div className="absolute bottom-0 right-0 w-2 h-2 sm:w-2.5 sm:h-2.5 bg-green-500 border border-white dark:border-gray-800 rounded-full"></div>
                    {/* Unread message count badge */}
                    {room.unread_count && room.unread_count > 0 && (
                      <div className="absolute -top-0.5 -right-0.5 w-4 h-4 sm:w-4.5 sm:h-4.5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                        {room.unread_count > 99 ? '99+' : room.unread_count}
                      </div>
                    )}
                  </div>
                  <div className="mt-0.5 text-center max-w-16 sm:max-w-20">
                    <p className={`text-xs truncate ${isSelected
                        ? 'text-blue-600 dark:text-blue-400 font-medium'
                        : 'text-gray-600 dark:text-gray-400'
                      }`}>
                      {otherUser?.full_name || room.name || 'User'}
                    </p>
                    {isSelected && (
                      <div className="w-0.5 h-0.5 bg-blue-500 rounded-full mx-auto mt-0.5"></div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Soi kèo Content */}
      {activeSubTab === 'betting' && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Soi kèo chuyên nghiệp</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Nhận tư vấn kèo từ các chuyên gia hàng đầu
            </p>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">A</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">Admin Tuấn Kiệt</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Chuyên gia soi kèo</p>
                  </div>
                </div>
                <button className="px-3 py-1 bg-blue-500 text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                  Liên hệ
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {!(activeSubTab === 'betting') && (
        <div
          className="flex-1 overflow-y-auto p-4 relative"
          ref={chatContainerRef}
          onScroll={handleScroll}
          style={getChatContainerStyle()}
        >
          {/* Loading indicator for older messages */}
          {(isLoadingMore || directMessagesLoading) && (
            <div className="text-center py-2">
              <div className="inline-flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                <span>Đang tải tin nhắn cũ...</span>
              </div>
            </div>
          )}

          {/* Messages */}
          <div className={`space-y-2 ${isMobile ? 'pb-36' : ''}`}>
            {/* All messages (real + auto) sorted by timestamp */}
            {allMessages.map(renderMessage)}
          </div>

          {/* Login to comment card */}
          {!isLoggedIn && (
            <div
              className="mt-4 p-4 bg-white dark:bg-custom-dark border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm cursor-pointer relative z-20 hover:bg-white dark:hover:bg-gray-700 transition-colors"
              onClick={() => onOpenAuthModal('login')}
              style={{
                // Ensure it's always visible on mobile
                ...(isMobile ? {
                  position: 'relative',
                  zIndex: 20,
                  marginBottom: '80px' // Add space for ChatInput
                } : {
                  marginTop: '150px'
                })
              }}
            >
              <div className="text-center">
                <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">Đăng nhập để bình luận</p>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Chat Input */}
      {!(activeSubTab === 'betting') && (
        <div
          className={isMobile ? "fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-custom-dark border-t border-gray-200 dark:border-gray-700" : "mb-5 md:mb-0"}
          style={getChatInputStyle()}
        >
          <ChatInput
            onSubmit={handleSubmit}
            isLoggedIn={isLoggedIn}
            replyTo={replyTo}
            onCancelReply={() => setReplyTo(null)}
            placeholder="Nhập tin nhắn riêng..."
            disabled={false}
            noBorderRadius={isMobile}
            onOpenAuthModal={onOpenAuthModal}
          />
        </div>
      )}
      
      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <div
          className="absolute right-4 z-20"
          style={{
            bottom: isMobile ? '80px' : '70px'
          }}
        >
          <button
            onClick={scrollToBottom}
            className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white shadow-lg hover:bg-blue-600 transition-all duration-200 hover:scale-105"
            title="Scroll xuống tin nhắn mới nhất"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}
