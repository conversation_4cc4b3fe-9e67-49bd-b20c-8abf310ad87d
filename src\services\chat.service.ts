import type {
  Chat<PERSON>essage,
  ChatResponse,
  ChatRoom,
  CreateChatData,
  MessagesResponse,
} from "@/types/chat.types";
import type { SupabaseClient } from "@supabase/supabase-js";

export class ChatService {
  private supabase: SupabaseClient;
  private activeSubscriptions = new Map<string, () => void>();
  private subscriptionCallbacks = new Map<
    string,
    (message: ChatMessage) => void
  >();

  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  // Method để cập nhật supabase client từ context
  setSupabaseClient(client: SupabaseClient) {
    this.supabase = client;
    // Khi client thay đổi, cần resubscribe tất cả channels
    // this.resubscribeAll();
  }

  // Method để re-subscribe tất cả active subscriptions
  //   private resubscribeAll() {
  //     console.log(
  //       "Re-subscribing all active subscriptions:",
  //       this.subscriptionCallbacks.size
  //     );

  //     // Unsubscribe tất cả subscriptions cũ
  //     this.activeSubscriptions.forEach((unsubscribe) => {
  //       try {
  //         unsubscribe();
  //       } catch (error) {
  //         console.warn("Error unsubscribing:", error);
  //       }
  //     });
  //     this.activeSubscriptions.clear();

  //     // Đợi một chút để đảm bảo connections cũ đã được cleanup
  //     setTimeout(() => {
  //       // Re-subscribe với client mới
  //       this.subscriptionCallbacks.forEach((callback, roomId) => {
  //         console.log("Re-subscribing to room:", roomId);
  //         const unsubscribe = this.subscribeToMessages(roomId, callback);
  //         this.activeSubscriptions.set(roomId, unsubscribe);
  //       });
  //     }, 100);
  //   }

  async getUserChatRooms(
    userId: string
  ): Promise<{ rooms: ChatRoom[]; error: Error | null }> {
    try {
      const { data, error } = await this.supabase.rpc(
        "get_user_rooms_with_messages",
        {
          user_uuid: userId,
        }
      );

      if (error) {
        return {
          rooms: [],
          error: new Error(error.message),
        };
      }

      const rooms: ChatRoom[] = data.map((room: Record<string, unknown>) => {
        const messages = room.messages as
          | Array<{ content: string; created_at: string }>
          | undefined;
        return {
          id: room.id as string,
          name: room.name as string,
          type: room.type as "direct" | "group" | "general" | "private",
          last_message:
            messages && messages.length > 0
              ? messages[messages.length - 1].content
              : undefined,
          last_message_at:
            messages && messages.length > 0
              ? messages[messages.length - 1].created_at
              : undefined,
          other_user:
            room.type === "direct"
              ? {
                  id: room.other_user_id as string,
                  full_name: room.other_user_name as string,
                  avatar_url: room.other_user_avatar as string | undefined,
                }
              : undefined,
        };
      });

      return {
        rooms,
        error: null,
      };
    } catch (error) {
      return {
        rooms: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getMessages(roomId: string): Promise<MessagesResponse> {
    try {
      const { data, error } = await this.supabase
        .from("messages")
        .select(
          `
                    id,
                    content,
                    created_at,
                    sender_id,
                    profiles!inner(full_name, avatar_url)
                `
        )
        .eq("room_id", roomId)
        .order("created_at", { ascending: true });

      if (error) {
        return {
          messages: [],
          error: new Error(error.message),
        };
      }

      const messages: ChatMessage[] = data.map(
        (msg: Record<string, unknown>) => {
          const profiles = msg.profiles as {
            full_name: string;
            avatar_url?: string;
          };
          return {
            id: msg.id as string,
            content: msg.content as string,
            created_at: msg.created_at as string,
            user_id: msg.sender_id as string,
            user: {
              full_name: profiles.full_name,
              avatar_url: profiles.avatar_url,
            },
          };
        }
      );

      return {
        messages,
        error: null,
      };
    } catch (error) {
      return {
        messages: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async sendMessage(
    roomId: string,
    senderId: string,
    content: string,
    retryCount = 0
  ): Promise<{ error: Error | null }> {
    try {
      console.log("this.supabase", this.supabase);
      const {
        data: { session },
      } = await this.supabase.auth.getSession();

      console.log("session:", session);

      const { data: user } = await this.supabase.auth.getUser();

      console.log("user:", user);

      console.log('roomId', roomId)
      console.log('senderId', senderId)

      const { error } = await this.supabase.from("messages").insert({
        content: content.trim(),
        room_id: roomId,
        sender_id: senderId,
      });

      if (error) {
        console.error("Send message error:", error);

        // Nếu lỗi liên quan đến connection và chưa retry quá 2 lần
        if (
          retryCount < 2 &&
          (error.message.includes("timeout") ||
            error.message.includes("connection") ||
            error.message.includes("network"))
        ) {
          console.log(
            `Connection error detected, retrying... (${retryCount + 1}/2)`
          );
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * (retryCount + 1))
          ); // Exponential backoff
          return this.sendMessage(roomId, senderId, content, retryCount + 1);
        }

        return {
          error: new Error(error.message),
        };
      }

      return {
        error: null,
      };
    } catch (error) {
      console.error("Send message catch error:", error);

      // Retry logic cho network errors
      if (
        retryCount < 2 &&
        error instanceof Error &&
        (error.message.includes("timeout") ||
          error.message.includes("fetch") ||
          error.message.includes("network"))
      ) {
        console.log(
          `Network error detected, retrying... (${retryCount + 1}/2)`
        );
        await new Promise((resolve) =>
          setTimeout(resolve, 1000 * (retryCount + 1))
        );
        return this.sendMessage(roomId, senderId, content, retryCount + 1);
      }

      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async createChat(
    chatData: CreateChatData,
    creatorId: string
  ): Promise<ChatResponse> {
    try {
      // Create chat room
      const { data: roomData, error: roomError } = await this.supabase
        .from("chat_rooms")
        .insert({
          name: chatData.name,
          type: chatData.type,
          created_by: creatorId,
        })
        .select()
        .single();

      if (roomError) {
        return {
          data: null,
          error: new Error(roomError.message),
        };
      }

      // Add creator as member
      const { error: memberError } = await this.supabase
        .from("chat_room_members")
        .insert({
          room_id: roomData.id,
          user_id: creatorId,
        });

      if (memberError) {
        return {
          data: null,
          error: new Error(memberError.message),
        };
      }

      // Add other members if provided
      if (chatData.members && chatData.members.length > 0) {
        const memberInserts = chatData.members.map((memberId) => ({
          room_id: roomData.id,
          user_id: memberId,
        }));

        const { error: membersError } = await this.supabase
          .from("chat_room_members")
          .insert(memberInserts);

        if (membersError) {
          return {
            data: null,
            error: new Error(membersError.message),
          };
        }
      }

      return {
        data: roomData,
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async deleteChat(roomId: string): Promise<{ error: Error | null }> {
    try {
      const { error } = await this.supabase
        .from("chat_rooms")
        .delete()
        .eq("id", roomId);

      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async joinChat(
    roomId: string,
    userId: string
  ): Promise<{ error: Error | null }> {
    try {
      const { error } = await this.supabase.from("chat_room_members").insert({
        room_id: roomId,
        user_id: userId,
      });

      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getChatRoom(
    roomId: string
  ): Promise<{ room: ChatRoom | null; error: Error | null }> {
    try {
      const { data, error } = await this.supabase
        .from("chat_rooms")
        .select("*")
        .eq("id", roomId)
        .single();

      return {
        room: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        room: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  subscribeToMessages(
    roomId: string,
    callback: (message: ChatMessage) => void
  ) {
    // Lưu callback để có thể re-subscribe sau này
    this.subscriptionCallbacks.set(roomId, callback);

    console.log("Subscribing to messages for room:", roomId);

    const subscription = this.supabase
      .channel(`messages:${roomId}`)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "messages",
          filter: `room_id=eq.${roomId}`,
        },
        async (payload) => {
          console.log("Received new message:", payload.new);
          const newMessage = payload.new;

          const { data: profileData } = await this.supabase
            .from("profiles")
            .select("full_name, avatar_url")
            .eq("id", newMessage.sender_id)
            .single();

          const formattedMessage: ChatMessage = {
            id: newMessage.id,
            content: newMessage.content,
            created_at: newMessage.created_at,
            user_id: newMessage.sender_id,
            user: {
              full_name: profileData?.full_name || "Unknown User",
              avatar_url: profileData?.avatar_url,
            },
          };

          callback(formattedMessage);
        }
      )
      .subscribe();

    const unsubscribe = () => {
      console.log("Unsubscribing from room:", roomId);
      subscription.unsubscribe();
      this.subscriptionCallbacks.delete(roomId);
      this.activeSubscriptions.delete(roomId);
    };

    // Lưu unsubscribe function
    this.activeSubscriptions.set(roomId, unsubscribe);

    return unsubscribe;
  }
}
