"use client";

import { useState } from 'react';
import MatchCard from '@/components/common/MatchCard';
import { MatchData } from '@/types/match';
import { MatchWithOrderInfo } from '@/hooks/useMatchOrderMapper';

export default function QuickBorderTest() {
  // Test data
  const testMatches: MatchWithOrderInfo[] = [
    // Case 1: Live match WITH order (should have RED border)
    {
      id: 'test-1',
      status: 'live',
      homeTeam: { name: 'Team A', logo: '', score: 1 },
      awayTeam: { name: 'Team B', logo: '', score: 0 },
      league: 'Test League',
      category: 'football',
      cards: { redAway: 0, redHome: 0, yellowAway: 0, yellowHome: 0 },
      odds: null,
      liveData: [],
      liveTrack: null,
      typeMatch: 'test',
      date: '2024-01-01',
      time: '15:00',
      links: [],
      viewFake: 0,
      liveFake: 0,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
      parseData: null,
      title: null,
      imageUrl: null,
      author: null,
      hashtags: null,
      timestamp: null,
      flv: null,
      _ownLeague: false,
      _ownHomeTeam: false,
      _ownAwayTeam: false,
      _ownCards: false,
      _ownOdds: false,
      _ownLiveTrack: false,
      _ownStatus: false,
      _ownDate: false,
      _ownTime: false,
      _ownParseData: false,
      key_sync: null,
      incidents: null,
      statistics: null,
      orderInfo: {
        order: '1',
        hasRedBorder: true,
        isFromSheet: true
      }
    },
    // Case 2: Live match WITHOUT order (should have NORMAL border)
    {
      id: 'test-2',
      status: 'live',
      homeTeam: { name: 'Team C', logo: '', score: 2 },
      awayTeam: { name: 'Team D', logo: '', score: 1 },
      league: 'Test League',
      category: 'football',
      cards: { redAway: 0, redHome: 0, yellowAway: 0, yellowHome: 0 },
      odds: null,
      liveData: [],
      liveTrack: null,
      typeMatch: 'test',
      date: '2024-01-01',
      time: '16:00',
      links: [],
      viewFake: 0,
      liveFake: 0,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
      parseData: null,
      title: null,
      imageUrl: null,
      author: null,
      hashtags: null,
      timestamp: null,
      flv: null,
      _ownLeague: false,
      _ownHomeTeam: false,
      _ownAwayTeam: false,
      _ownCards: false,
      _ownOdds: false,
      _ownLiveTrack: false,
      _ownStatus: false,
      _ownDate: false,
      _ownTime: false,
      _ownParseData: false,
      key_sync: null,
      incidents: null,
      statistics: null,
      orderInfo: {
        order: '',
        hasRedBorder: false,
        isFromSheet: false
      }
    },
    // Case 3: Non-live match WITH order (should have NORMAL border)
    {
      id: 'test-3',
      status: 'finished',
      homeTeam: { name: 'Team E', logo: '', score: 0 },
      awayTeam: { name: 'Team F', logo: '', score: 3 },
      league: 'Test League',
      category: 'football',
      cards: { redAway: 0, redHome: 0, yellowAway: 0, yellowHome: 0 },
      odds: null,
      liveData: [],
      liveTrack: null,
      typeMatch: 'test',
      date: '2024-01-01',
      time: '17:00',
      links: [],
      viewFake: 0,
      liveFake: 0,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
      parseData: null,
      title: null,
      imageUrl: null,
      author: null,
      hashtags: null,
      timestamp: null,
      flv: null,
      _ownLeague: false,
      _ownHomeTeam: false,
      _ownAwayTeam: false,
      _ownCards: false,
      _ownOdds: false,
      _ownLiveTrack: false,
      _ownStatus: false,
      _ownDate: false,
      _ownTime: false,
      _ownParseData: false,
      key_sync: null,
      incidents: null,
      statistics: null,
      orderInfo: {
        order: '2',
        hasRedBorder: false,
        isFromSheet: true
      }
    }
  ];

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Quick Border Test</h2>
      
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-2">Test Cases</h3>
        <div className="text-sm space-y-1">
          <div>🔴 <strong>Red Border:</strong> Live + có Order (test-1)</div>
          <div>⚪ <strong>Normal Border:</strong> Live + không có Order (test-2)</div>
          <div>⚪ <strong>Normal Border:</strong> Finished + có Order (test-3)</div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {testMatches.map((match, index) => (
          <div key={match.id} className="space-y-2">
            <div className="text-sm font-medium text-center">
              Case {index + 1}: {match.status} + order "{match.orderInfo?.order || 'none'}"
            </div>
            
            <MatchCard
              match={match}
              variant="default"
              onClick={() => {
                console.log('Test match clicked:', {
                  id: match.id,
                  status: match.status,
                  order: match.orderInfo?.order,
                  hasRedBorder: match.orderInfo?.hasRedBorder
                });
              }}
            />
            
            <div className="text-xs text-center p-2 bg-gray-100 rounded">
              Expected: {match.orderInfo?.hasRedBorder ? '🔴 Red Border' : '⚪ Normal Border'}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Verification</h3>
        <div className="text-sm space-y-1">
          <div>✅ Chỉ Case 1 (live + order) có viền đỏ</div>
          <div>✅ Case 2 (live + no order) có viền thường</div>
          <div>✅ Case 3 (finished + order) có viền thường</div>
        </div>
      </div>
    </div>
  );
}
