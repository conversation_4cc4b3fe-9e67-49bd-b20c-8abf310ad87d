"use client";

import AuthModal from "@/components/AuthModal";
import VideoPlayer from "@/components/common/VideoPlayer";
import GeneralChatTab from "@/components/features/GeneralChatTab";
import DirectChatTab from "@/components/features/DirectChatTab";
import CommentatorTab from "@/components/features/CommentatorTab";
import HeadToHeadTab from "@/components/features/HeadToHeadTab";
import LineupTab from "@/components/features/LineupTab";
import RelatedMatchesTab from "@/components/features/RelatedMatchesTab";
import StatsTab from "@/components/features/StatsTab";
import { formatTime, getStatusText } from "@/lib/utils";
import Image from "next/image";
import { useEffect, useState } from "react";
import {
  MobileActionButtons,
  MobileScreenLockButton,
} from "./MobileComponents";
import BannerChat from "@/components/features/BannerChat";
import useLiveDetailHook from "./useHook";

type Params = { match?: string; streamer?: string; id?: string };

export default function LiveDetail({ params }: { params: Promise<Params> }) {
  // Local state for keyboard detection∏
  const [isIOS, setIsIOS] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  // System message state
  const [systemMessageSent, setSystemMessageSent] = useState(false);

  const {
    // States
    activeTab,
    setActiveTab,
    isLoggedIn,
    isAuthModalOpen,
    authMode,
    setAuthMode,
    videoUrl,
    matchData,
    blvInfo,
    isClient,
    videoHeight,
    mobileViewportHeight,
    videoTopPosition,
    isMobile,
    loading,
    isScreenLocked,
    chatContainerRef,
    videoRef,
    selectedRoomId,

    // Params
    matchId,
    streamerName,
    matchSlug,

    // Handlers
    handleScroll,
    handleLoginSuccess,
    openAuthModal,
    closeAuthModal,
    setAuthModalMode,
    handleTabChange,
    toggleScreenLock,
    handleHomeClick,
  } = useLiveDetailHook(params);

  // Desktop tabs (without "Trận đấu liên quan")
  const desktopTabs = [
    {
      index: 0,
      label: "Home",
      icon: "/icon/home.svg",
      iconActive: "/icon/home-active.svg",
    },
    {
      index: 1,
      label: "Chat",
      icon: "/icon/chat.svg",
      iconActive: "/icon/chat-active.svg",
    },
    {
      index: 2,
      label: "Chat riêng",
      icon: "/icon/chat.svg",
      iconActive: "/icon/chat-active.svg",
    },
    {
      index: 3,
      label: "Thông Số",
      icon: "/icon/thong-so.svg",
      iconActive: "/icon/thong-so-active.svg",
    },
    // { index: 4, label: "Bình luận viên", icon: "/icon/dien-bien.svg", iconActive: "/icon/dien-bien-active.svg" },
    // { index: 5, label: "Đội Hình", icon: "/icon/doi-hinh.svg", iconActive: "/icon/doi-hinh-active.svg" },
    // { index: 6, label: "Tỷ Lệ", icon: "/icon/ti-le-keo.svg", iconActive: "/icon/ty-le-active.svg" },
    // { index: 7, label: "Đối Đầu", icon: "/icon/doi-dau.svg", iconActive: "/icon/doi-dau-active.svg" },
    // { index: 8, label: "Thông Tin", icon: "/icon/bxh.svg", iconActive: "/icon/thong-tin-active.svg" },
  ];

  // Mobile tabs (with "Trận đấu liên quan")
  const mobileTabs = [
    {
      index: 0,
      label: "Home",
      icon: "/icon/home.svg",
      iconActive: "/icon/home-active.svg",
    },
    {
      index: 1,
      label: "Chat",
      icon: "/icon/chat.svg",
      iconActive: "/icon/chat-active.svg",
    },
    {
      index: 2,
      label: "Chat riêng",
      icon: "/icon/chat.svg",
      iconActive: "/icon/chat-active.svg",
    },
    {
      index: 3,
      label: "Thông Số",
      icon: "/icon/thong-so.svg",
      iconActive: "/icon/thong-so-active.svg",
    },
    // { index: 4, label: "Bình luận viên", icon: "/icon/dien-bien.svg", iconActive: "/icon/dien-bien-active.svg" },
    // { index: 5, label: "Đội Hình", icon: "/icon/doi-hinh.svg", iconActive: "/icon/doi-hinh-active.svg" },
    // { index: 6, label: "Tỷ Lệ", icon: "/icon/ti-le-keo.svg", iconActive: "/icon/ty-le-active.svg" },
    // { index: 7, label: "Đối Đầu", icon: "/icon/doi-dau.svg", iconActive: "/icon/doi-dau-active.svg" },
    // { index: 8, label: "Thông Tin", icon: "/icon/bxh.svg", iconActive: "/icon/thong-tin-active.svg" },
    {
      index: 9,
      label: "Trận đấu liên quan",
      icon: "/icon/tran-khac.svg",
      iconActive: "/icon/tran-khac-active.svg",
    },
  ];

  // Use appropriate tabs based on screen size
  const tabs = isMobile ? mobileTabs : desktopTabs;

  // Detect iOS and keyboard state
  useEffect(() => {
    // Detect iOS
    const isIOSDevice =
      /iPad|iPhone|iPod/.test(navigator.userAgent) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    setIsIOS(isIOSDevice);

    // Detect keyboard state
    const updateKeyboardState = () => {
      if (isMobile) {
        if (isIOSDevice) {
          // iOS: Use visual viewport to detect keyboard
          const visualViewport = window.visualViewport;
          if (visualViewport) {
            const keyboardHeight = Math.max(
              0,
              window.innerHeight - visualViewport.height
            );
            setIsKeyboardOpen(keyboardHeight > 0);
          }
        } else {
          // Android: Use window height difference
          const initialHeight =
            window.visualViewport?.height || window.innerHeight;
          const currentHeight = window.innerHeight;
          const keyboardHeight = Math.max(0, initialHeight - currentHeight);
          setIsKeyboardOpen(keyboardHeight > 0);
        }
      }
    };

    // Initial check
    updateKeyboardState();

    // Listen for keyboard events
    const handleResize = () => updateKeyboardState();
    const handleOrientationChange = () => {
      setTimeout(updateKeyboardState, 100);
    };

    // iOS specific keyboard events
    const handleIOSKeyboardShow = () => {
      setIsKeyboardOpen(true);
      setTimeout(updateKeyboardState, 100);
    };

    const handleIOSKeyboardHide = () => {
      setIsKeyboardOpen(false);
      setTimeout(updateKeyboardState, 100);
    };

    window.addEventListener("resize", handleResize);
    window.addEventListener("orientationchange", handleOrientationChange);

    // iOS keyboard events
    if (isIOSDevice) {
      window.addEventListener("focusin", handleIOSKeyboardShow);
      window.addEventListener("focusout", handleIOSKeyboardHide);
    }

    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", updateKeyboardState);
    }

    return () => {

      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleOrientationChange);

      if (isIOSDevice) {
        window.removeEventListener("focusin", handleIOSKeyboardShow);
        window.removeEventListener("focusout", handleIOSKeyboardHide);
      }

      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          updateKeyboardState
        );
      }
    };
  }, [isMobile]);

  // Disable page scroll on mobile (but allow when keyboard is open)
  useEffect(() => {
    if (isMobile) {
      if (isKeyboardOpen) {
        // When keyboard is open, allow scroll but keep body fixed to prevent page scroll
        document.body.style.overflow = "hidden";
        document.body.style.overflowX = "hidden"; // Ngăn scroll ngang
        document.body.style.height = "100vh";
        document.body.style.position = "fixed";
        document.body.style.top = "0";
        document.body.style.left = "0";
        document.body.style.right = "0";
        document.body.style.bottom = "0";
        document.body.style.touchAction = "pan-y";
        (
          document.body.style as CSSStyleDeclaration & {
            WebkitOverflowScrolling?: string;
          }
        ).WebkitOverflowScrolling = "touch";
        document.body.style.overscrollBehavior = "contain";

        // Also adjust for html
        document.documentElement.style.overflow = "hidden";
        document.documentElement.style.overflowX = "hidden"; // Ngăn scroll ngang
        document.documentElement.style.height = "100vh";
        document.documentElement.style.touchAction = "pan-y";
      } else {
        // When keyboard is closed, allow normal scroll but prevent page scroll
        document.body.style.overflow = "hidden";
        document.body.style.overflowX = "hidden"; // Ngăn scroll ngang
        document.body.style.height = "100vh";
        document.body.style.position = "fixed";
        document.body.style.top = "0";
        document.body.style.left = "0";
        document.body.style.right = "0";
        document.body.style.bottom = "0";
        document.body.style.touchAction = "pan-y";
        (
          document.body.style as CSSStyleDeclaration & {
            WebkitOverflowScrolling?: string;
          }
        ).WebkitOverflowScrolling = "touch";
        document.body.style.overscrollBehavior = "contain";

        // Also adjust for html
        document.documentElement.style.overflow = "hidden";
        document.documentElement.style.overflowX = "hidden"; // Ngăn scroll ngang
        document.documentElement.style.height = "100vh";
        document.documentElement.style.touchAction = "pan-y";
      }
    } else {
      // Re-enable scroll for desktop
      document.body.style.overflow = "";
      document.body.style.overflowX = "";
      document.body.style.height = "";
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.left = "";
      document.body.style.right = "";
      document.body.style.bottom = "";
      document.body.style.touchAction = "";
      (
        document.body.style as CSSStyleDeclaration & {
          WebkitOverflowScrolling?: string;
        }
      ).WebkitOverflowScrolling = "";
      document.body.style.overscrollBehavior = "";

      document.documentElement.style.overflow = "";
      document.documentElement.style.overflowX = "";
      document.documentElement.style.height = "";
      document.documentElement.style.touchAction = "";
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "";
      document.body.style.overflowX = "";
      document.body.style.height = "";
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.left = "";
      document.body.style.right = "";
      document.body.style.bottom = "";
      document.body.style.touchAction = "";
      (
        document.body.style as CSSStyleDeclaration & {
          WebkitOverflowScrolling?: string;
        }
      ).WebkitOverflowScrolling = "";
      document.body.style.overscrollBehavior = "";

      document.documentElement.style.overflow = "";
      document.documentElement.style.overflowX = "";
      document.documentElement.style.height = "";
      document.documentElement.style.touchAction = "";
    };
  }, [isMobile, isKeyboardOpen]);

  // Add system message after 30 seconds when chat tab is active
  useEffect(() => {
    if (activeTab === 1 && !systemMessageSent) {
      // Chat tab is active
      const timer = setTimeout(() => {
        // Create system message object
        const systemMessage = {
          id: `system-${Date.now()}`,
          content:
            "Quà tặng tân thủ lên tới 10 triệu đồng trong tháng. Đặc biệt Megalive hàng tuần quà tặng lên tới 288 triệu.",
          created_at: new Date().toISOString(),
          user_id: "system",
          user: {
            full_name: "Tin nhắn hệ thống",
            avatar_url: "/icon/social.svg", // System icon
          },
        };

        // Add system message to chat messages
        // This will be handled by ChatTab component
        console.log("Adding system message to chat:", systemMessage);
        setSystemMessageSent(true);
      }, 3 * 60 * 1000); // 3 minutes

      return () => clearTimeout(timer);
    }
  }, [activeTab, systemMessageSent]);

  // Reset system message when switching away from chat tab
  useEffect(() => {
    if (activeTab !== 1) {
      setSystemMessageSent(false);
    }
  }, [activeTab]);

  return (
    <>
      <div
        className="min-h-screen bg-white dark:bg-custom-dark w-full"
        style={{
          margin: 0,
          padding: 0,
          scrollBehavior: "auto",
          // Disable page scroll on mobile
          ...(isMobile
            ? {
                height: "100vh",
                overflow: "hidden",
                overflowX: "hidden", // Ngăn scroll ngang
                position: "fixed",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                touchAction: "none",
                WebkitOverflowScrolling: "auto",
                overscrollBehavior: "none",
              }
            : {}),
        }}
      >
        <div
          className="w-full"
          style={{
            // Enable scroll within the fixed container on mobile
            ...(isMobile
              ? {
                  height: "100vh",
                  overflow: "auto",
                  overflowX: "hidden", // Ngăn scroll ngang
                  WebkitOverflowScrolling: "touch",
                  touchAction: "pan-y",
                  position: "relative",
                  maxWidth: "100vw", // Đảm bảo không vượt quá viewport width
                }
              : {}),
          }}
        >
          {/* Main Content */}
          <div
            className={`${isScreenLocked ? "pt-0" : "py-6"} px-0`}
            style={{
              marginTop:
                isScreenLocked && videoHeight > 0 ? `${videoHeight}px` : "0",
            }}
          >
            {isScreenLocked && (
              <div className="fixed top-0 left-0 right-0 z-50">
                <div className="w-full" ref={videoRef}>
                  <VideoPlayer
                    videoUrl={videoUrl}
                    autoPlay={true}
                    muted={true}
                    volume={0.7}
                    theme="#0f1214"
                    className="match-card-enhanced"
                    breakOutContainer={true}
                  />
                </div>
              </div>
            )}
            <div className="flex flex-col lg:grid lg:grid-cols-10 gap-2 w-full max-w-full overflow-x-hidden">
              <div className="w-full lg:col-span-7 flex-shrink-0 max-w-full lg:max-w-none overflow-x-hidden">
                {/* Loading State */}
                {loading && (
                  <div className="bg-white dark:bg-custom-dark rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm p-8 mb-2">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-3 text-gray-600 dark:text-gray-400">
                        Đang tải thông tin trận đấu...
                      </span>
                    </div>
                  </div>
                )}

                {/* Error State */}
                {!loading && !matchData && (
                  <div className="bg-white dark:bg-custom-dark rounded-lg border border-red-200 dark:border-red-700 shadow-sm p-8 mb-2">
                    <div className="text-center text-red-600 dark:text-red-400">
                      <p className="font-semibold">
                        Không thể tải thông tin trận đấu
                      </p>
                      <p className="text-sm mt-2">Vui lòng thử lại sau</p>
                    </div>
                  </div>
                )}

                {/* Match Info Header - Top Bar - Outside Card - Ẩn khi khóa */}
                {!loading && matchData && !isScreenLocked && !isMobile && (
                  <div className="bg-white dark:bg-custom-dark rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm p-3 mb-2 relative z-10">
                    <div className="w-full flex flex-wrap text-sm lg:text-lg items-center p-1 font-bold break-words gap-1">
                      <div className="h-7 w-2 rounded-[10px] bg-gradient-to-r from-[#1E40AF] to-[#3B82F6] flex-shrink-0"></div>
                      <div className="text-center">
                        <span className="">{matchData?.league}</span>
                        <span> - </span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        <div className="home_info flex items-center gap-2">
                          {matchData?.homeTeam?.name || "Team 1"}
                        </div>
                        <div>VS</div>
                        <div className="away_info flex items-center gap-2">
                          {matchData?.awayTeam?.name || "Team 2"}
                        </div>
                      </div>
                      <span> - </span>
                      <span>
                        {getStatusText(matchData?.status) || "Status"}
                      </span>
                      <span> - </span>
                      <span>{formatTime(matchData?.time || "") || "Date"}</span>
                    </div>
                  </div>
                )}

                {/* Main Match Card - Contains Status + Video */}
                {!loading && matchData && (
                  <div
                    className="bg-white dark:bg-custom-dark rounded-lg shadow-sm overflow-hidden w-full max-w-full overflow-x-hidden"
                    ref={videoRef}
                  >
                    {/* Video Player - Inside Card */}
                    {!isScreenLocked && (
                      <div
                        className="p-0 w-full max-w-full relative z-0"
                        style={{ paddingTop: isMobile ? "28px" : "0" }}
                      >
                        <div className="w-full max-w-full overflow-hidden">
                          <VideoPlayer
                            videoUrl={videoUrl}
                            autoPlay={true}
                            muted={true}
                            volume={0.7}
                            theme="#0f1214"
                            className="match-card-enhanced w-full max-w-full"
                            breakOutContainer={false}
                          />
                        </div>
                      </div>
                    )}

                    {/* Match Status - Above Video - Ẩn khi khóa */}
                    {!isScreenLocked && !isMobile && (
                      <div className="py-2">
                        <div className="flex items-center justify-center gap-3 rounded py-1 text-sm lg:text-lg font-bold">
                          <div className="home_info flex items-center gap-2">
                            {matchData?.homeTeam?.name || "Team 1"}
                            {matchData?.homeTeam?.logo && (
                              <img
                                alt={matchData.homeTeam.name}
                                loading="lazy"
                                width="35"
                                height="35"
                                decoding="async"
                                src={matchData.homeTeam.logo}
                                className="w-8 h-8 object-contain"
                              />
                            )}
                          </div>
                          <div className="score_info flex items-center gap-1">
                            <span className="text-primary text-xl font-semibold">
                              {matchData?.homeTeam?.score || 0}
                            </span>
                            <span className="font-semibold">-</span>
                            <span className="text-primary text-xl font-semibold">
                              {matchData?.awayTeam?.score || 0}
                            </span>
                          </div>
                          <div className="away_info flex items-center gap-2">
                            {matchData?.awayTeam?.logo && (
                              <img
                                alt={matchData.awayTeam.name}
                                loading="lazy"
                                width="35"
                                height="35"
                                decoding="async"
                                src={matchData.awayTeam.logo}
                                className="w-8 h-8 object-contain"
                              />
                            )}
                            {matchData?.awayTeam?.name || "Team 2"}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Commentator Section - Inside Video Card */}
                    <div className="border-t border-gray-200 dark:border-gray-700">
                      {/* Match Info Section with Modern Design */}
                      <div className="w-full rounded pt-1" id="app-match-info">
                        {/* Action Buttons Row - Mobile Only - Luôn hiển thị */}
                        <MobileActionButtons blvInfo={blvInfo || {}} />

                        {/* Main Match Info Card */}
                        <div className="hidden sm:block mt-1 w-full shadow-[0px_0px_4px_4px_#1E40AF40] rounded border-2 p-2 border-blue-600 relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 bg-cover bg-no-repeat opacity-30 rounded"></div>
                          <div className="relative z-10">
                            <div className="flex text-base w-full overflow-hidden">
                              <div className="flex flex-col lg:flex-row items-center w-full min-w-0">
                                {/* Left Section - BLV Info */}
                                <div className="flex items-start w-full lg:w-[60%] min-w-0 max-w-full">
                                  <div className="flex flex-col gap-2 lg:gap-4 w-full min-w-0">
                                    <div className="flex items-center min-w-0 w-full">
                                      {/* Match Info with Gradient Design */}
                                      <div className="flex gap-1 sm:gap-2 lg:gap-3 min-w-0 flex-1 overflow-hidden mx-2">
                                        <div className="bg-gradient-to-l from-[#3B82F6] to-[#1E40AF] flex items-center rounded-bl-4xl rounded-tr-3xl p-0.5 px-1 lg:px-2 text-white -ml-2 sm:-ml-3 lg:-ml-4 min-w-0 flex-1 overflow-hidden">
                                          <div className="bg-white text-black font-bold p-1 px-1 sm:px-2 mx-1 ml-1 sm:ml-2 lg:ml-3 rounded-bl-xl rounded-tr-3xl min-w-0 w-16 sm:w-20 lg:w-auto flex-shrink-0">
                                            <div
                                              className="bg-gradient-to-l from-[#3B82F6] to-[#1E40AF] bg-clip-text text-transparent font-bold truncate text-xs lg:text-base"
                                              title={`BLV ${
                                                blvInfo?.displayName ||
                                                matchData?.liveData?.[0]?.blv ||
                                                streamerName
                                              }`}
                                            >
                                              {blvInfo?.displayName ||
                                                matchData?.liveData?.[0]?.blv ||
                                                streamerName}
                                            </div>
                                          </div>
                                          <div className="relative flex-1 min-w-0 overflow-hidden">
                                            <div className="flex items-center px-1 sm:px-2 gap-1 lg:gap-1 min-w-0 overflow-x-auto whitespace-nowrap scroll-smooth scrollbar-hide">
                                              <span
                                                className="text-xs lg:text-base font-semibold whitespace-nowrap lg:truncate"
                                                title={
                                                  matchData?.homeTeam?.name
                                                }
                                              >
                                                {matchData?.homeTeam?.name}
                                              </span>
                                              <span className="text-sm lg:text-xl font-semibold flex-shrink-0">
                                                {matchData?.homeTeam?.score}
                                              </span>
                                              <span className="font-semibold flex-shrink-0 text-xs lg:text-base">
                                                -
                                              </span>
                                              <span className="text-sm lg:text-xl font-semibold flex-shrink-0">
                                                {matchData?.awayTeam?.score}
                                              </span>
                                              <span
                                                className="text-xs lg:text-base font-semibold whitespace-nowrap lg:truncate"
                                                title={
                                                  matchData?.awayTeam?.name
                                                }
                                              >
                                                {matchData?.awayTeam?.name}
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    {/* BLV KHÁC Section */}
                                    {/* <div className="flex items-center border border-blue-600 px-1 overflow-hidden flex-shrink-0 max-w-full rounded-bl-3xl rounded-tr-3xl">
                                      <div className="bg-gradient-to-l from-[#3B82F6] to-[#1E40AF] rounded-bl-3xl rounded-tr-3xl p-1 px-2 sm:px-2 text-white whitespace-nowrap text-xs lg:text-sm flex-shrink-0">
                                        BLV KHÁC
                                      </div>
                                      <div className="flex items-center min-w-0 flex-1 overflow-hidden">
                                        <div className="w-full !mx-1 min-w-0 !max-w-full">
                                          <div className="flex gap-2 overflow-x-auto scrollbar-hide scroll-smooth-x">
                                            {[
                                              { id: 'Qatj7nC7S4N9jX0KfY2xZ86KFtB3', name: 'Giàng A Sếu', avatar: '/ngoaihangtv.png' },
                                              { id: 'C8M2c8FRpeaSSeThWpPsVwKom0n1', name: 'Giàng A Pháo', avatar: '/ngoaihangtv.png' },
                                              { id: 'D9N3d9GSqfbTTfUiXqQwLpn1o0o2', name: 'Giàng A Ly', avatar: '/ngoaihangtv.png' },
                                              { id: 'E0O4e0HTrgbUUgVjYrRxMqo2p1p3', name: 'Giàng A Hổ', avatar: '/ngoaihangtv.png' }
                                            ].map((blv, index) => (
                                              <a key={index} className="flex items-center gap-1 px-1 sm:px-2 min-w-0 hover:bg-gray-50 rounded transition-colors whitespace-nowrap flex-shrink-0" href={`/truc-tiep/${matchSlug}/streamer/${blv.id}`}>
                                                <div className="">
                                                  <div className="size-5 sm:size-6 flex-shrink-0" style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' }}>
                                                    <Image
                                                      src={blv.avatar}
                                                      alt={blv.name}
                                                      width={24}
                                                      height={24}
                                                      className="w-full h-full object-cover"
                                                    />
                                                  </div>
                                                </div>
                                                <span className="text-xs font-medium truncate">{blv.name}</span>
                                              </a>
                                            ))}
                                          </div>
                                        </div>
                                      </div>
                                    </div> */}
                                  </div>
                                </div>

                                {/* Right Section - Sponsor Logos & Follow Button */}
                                <div className="items-center hidden lg:flex w-full lg:w-[45%] h-full justify-end lg:justify-center lg:gap-5 mt-2 lg:mt-0 lg:pl-4">
                                  <div className="flex items-center gap-3 border-2 rounded-lg h-full px-4">
                                    <a
                                      target="_blank"
                                      className="lg:inline-flex hidden items-center justify-center h-12 lg:h-14 xl:h-16 min-w-20 xl:min-w-28"
                                      href="https://ngoaihangtv.me/"
                                    >
                                      <Image
                                        alt="KUDV Logo"
                                        loading="lazy"
                                        width={100}
                                        height={60}
                                        decoding="async"
                                        className="object-contain h-8 lg:h-10 xl:h-12 w-auto max-w-none"
                                        src="/vendor/ok-logo.png"
                                      />
                                    </a>
                                  </div>
                                  <div className="hidden lg:flex flex-col items-start flex-shrink-0 ml-2">
                                    <div className="flex items-center text-xs xl:text-sm font-light whitespace-nowrap">
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16"
                                        height="17"
                                        viewBox="0 0 16 17"
                                        fill="none"
                                      >
                                        <path
                                          d="M8.55467 14.8668C10.6387 14.4495 13.3333 12.9501 13.3333 9.07347C13.3333 5.54613 10.7513 3.1968 8.89467 2.11747C8.482 1.87747 8 2.1928 8 2.66947V3.88813C8 4.84947 7.596 6.60413 6.47333 7.33413C5.9 7.7068 5.28 7.1488 5.21067 6.4688L5.15333 5.91013C5.08667 5.2608 4.42533 4.8668 3.90667 5.2628C2.974 5.9728 2 7.21947 2 9.0728C2 13.8135 5.526 14.9995 7.28867 14.9995C7.39178 14.9995 7.49933 14.9961 7.61133 14.9895C6.74067 14.9155 5.33333 14.3755 5.33333 12.6288C5.33333 11.2621 6.33 10.3388 7.08733 9.8888C7.29133 9.7688 7.52933 9.92547 7.52933 10.1621V10.5555C7.52933 10.8555 7.646 11.3255 7.92267 11.6468C8.236 12.0108 8.69533 11.6295 8.732 11.1508C8.744 11.0001 8.896 10.9041 9.02667 10.9801C9.454 11.2301 10 11.7635 10 12.6288C10 13.9941 9.24733 14.6221 8.55467 14.8668ZM3.5 8.25015C3.5 7.25559 3.89509 6.30176 4.59835 5.5985C5.30161 4.89524 6.25544 4.50015 7.25 4.50015H10.25C10.4489 4.50015 10.6397 4.57916 10.7803 4.71982C10.921 4.86047 11 5.05123 11 5.25015C11 5.44906 10.921 5.63982 10.7803 5.78048C10.6397 5.92113 10.4489 6.00015 10.25 6.00015H7.25C6.65326 6.00015 6.08097 6.2372 5.65901 6.65916C5.23705 7.08111 5 7.65341 5 8.25015V17.2501C5 17.8469 5.23705 18.4192 5.65901 18.8411C6.08097 19.2631 6.65326 19.5001 7.25 19.5001H16.25C16.8467 19.5001 17.419 19.2631 17.841 18.8411C18.2629 18.4192 18.5 17.8469 18.5 17.2501V15.7501C18.5 15.5512 18.579 15.3605 18.7197 15.2198C18.8603 15.0792 19.0511 15.0001 19.25 15.0001C19.4489 15.0001 19.6397 15.0792 19.7803 15.2198C19.921 15.3605 20 15.5512 20 15.7501V17.2501C20 18.2447 19.6049 19.1985 18.9017 19.9018C18.1984 20.6051 17.2446 21.0001 16.25 21.0001H7.25C6.25544 21.0001 5.30161 20.6051 4.59835 19.9018C3.89509 19.1985 3.5 18.2447 3.5 17.2501V8.25015Z"
                                          fill="url(#paint0_linear_2295_24237)"
                                        ></path>
                                        <defs>
                                          <linearGradient
                                            id="paint0_linear_2295_24237"
                                            x1="13.3333"
                                            y1="8.51731"
                                            x2="2"
                                            y2="8.51731"
                                            gradientUnits="userSpaceOnUse"
                                          >
                                            <stop stopColor="#3B82F6"></stop>
                                            <stop
                                              offset="1"
                                              stopColor="#1E40AF"
                                            ></stop>
                                          </linearGradient>
                                        </defs>
                                      </svg>
                                      <span
                                        className="text-xs font-normal"
                                        title="5226 người theo dõi"
                                      >
                                        5226 theo dõi
                                      </span>
                                    </div>
                                    <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow-sm hover:bg-primary/90 py-2 h-7 rounded-full px-2 lg:h-8 bg-gradient-to-r from-[#1E40AF] to-[#3B82F6]">
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="lucide lucide-circle-plus"
                                      >
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M8 12h8"></path>
                                        <path d="M12 8v8"></path>
                                      </svg>
                                      <span className="text-xs">Theo dõi</span>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Chat Sidebar - 3 columns */}
              <aside className="w-full lg:col-span-3 flex-shrink-0 overflow-x-hidden">
                <div
                  className="bg-white dark:bg-custom-dark lg:rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm flex flex-col lg:mx-0 -mx-4 lg:h-[800px]"
                  style={{
                    height: isMobile
                      ? mobileViewportHeight > 0 && videoTopPosition > 0
                        ? `${mobileViewportHeight - videoTopPosition - 200}px`
                        : `${mobileViewportHeight - 200}px`
                      : undefined,
                    minHeight: isMobile ? "300px" : undefined,
                    maxHeight: isMobile ? "65vh" : undefined,
                    // Ensure proper scrolling on mobile
                    ...(isMobile
                      ? {
                          position: "relative",
                          overflow: "visible",
                          zIndex: 10,
                        }
                      : {}),
                  }}
                >
                  {/* Tab Navigation */}
                  <div className="p-2 overflow-x-auto">
                    <div className="flex min-w-max gap-1">
                      {tabs.map((tab) => (
                        <button
                          key={tab.index}
                          onClick={() =>
                            tab.index === 0
                              ? handleHomeClick()
                              : setActiveTab(tab.index)
                          }
                          className={`flex-shrink-0 px-1 sm:px-2 py-0 text-center text-xs font-medium transition-all duration-200 whitespace-nowrap rounded-full flex items-center gap-0 ${
                            activeTab === tab.index
                              ? "text-blue-600 border-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20 shadow-sm"
                              : "text-gray-500 dark:text-gray-400 border-0 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-white dark:hover:bg-gray-700"
                          } ${tab.index === 8 ? "lg:hidden" : ""}`}
                        >
                          {activeTab === tab.index && (
                            <span className="font-large text-sm sm:text-md">
                              {tab.label}
                            </span>
                          )}
                          <div className="w-8 h-6 sm:w-10 sm:h-7 flex items-center justify-center">
                            <Image
                              src={
                                activeTab === tab.index
                                  ? tab.iconActive
                                  : tab.icon
                              }
                              alt={tab.label}
                              width={30}
                              height={30}
                              className="w-6 h-6 sm:w-8 sm:h-8 transition-all duration-200"
                            />
                          </div>
                        </button>
                      ))}

                      {/* Screen Lock Button - Mobile Only */}
                      <MobileScreenLockButton
                        isScreenLocked={isScreenLocked}
                        toggleScreenLock={toggleScreenLock}
                      />
                    </div>
                  </div>

                  {/* Tab Content */}
                  <div
                    className="flex-1 overflow-y-auto relative min-h-0"
                    ref={chatContainerRef}
                    onScroll={handleScroll}
                    style={{
                      scrollBehavior: "smooth",
                      WebkitOverflowScrolling: "touch",
                      height: isMobile
                        ? mobileViewportHeight > 0 && videoTopPosition > 0
                          ? `${mobileViewportHeight - videoTopPosition - 120}px`
                          : `${mobileViewportHeight - 200}px`
                        : undefined,
                      minHeight: isMobile ? "150px" : undefined,
                      // Ensure proper scrolling on mobile
                      ...(isMobile
                        ? {
                            position: "relative",
                            overflow: "visible",
                            touchAction: "pan-y",
                            zIndex: 5,
                          }
                        : {}),
                    }}
                  >
                    {activeTab === 0 && (
                      /* Home Tab */
                      <div className="h-full flex flex-col">
                        <div className="flex-1 min-h-0 overflow-y-auto p-6 space-y-4">
                          <div className="text-center text-lg font-semibold text-gray-900 dark:text-white mb-6">
                            Trang chủ
                          </div>
                          <div className="text-center text-gray-500 dark:text-gray-400 text-sm py-8">
                            Chào mừng bạn đến với trang trực tiếp
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === 1 && (
                      <div className="h-full relative flex flex-col">
                        {/* Banner Thông số lên kèo - chỉ hiển thị trong tab chat và khi đã login */}
                        {/* {isLoggedIn && ( */}
                          <BannerChat
                            onOpenAuthModal={setAuthModalMode}
                            onTabChange={handleTabChange}
                          />
                        {/* )} */}
                        <div className="flex-1 min-h-0 overflow-hidden flex flex-col">
                          <div
                            className="flex-1 min-h-0 overflow-y-auto"
                            ref={chatContainerRef}
                            onScroll={handleScroll}
                            style={{
                              paddingBottom: isScreenLocked ? "50px" : "0px",
                              // Mobile: Allow ChatTab to scroll
                              ...(isMobile
                                ? {
                                    position: "relative",
                                    zIndex: 1,
                                    overflow: "auto", // Allow scrolling on mobile
                                    height: "100%", // Give full height to ChatTab
                                  }
                                : {}),
                            }}
                          >
                            <GeneralChatTab
                              isLoggedIn={isLoggedIn}
                              onOpenAuthModal={openAuthModal}
                              matchId={matchId}
                              systemMessage={
                                systemMessageSent
                                  ? {
                                      id: `system-${Date.now()}`,
                                      content:
                                        "Quà tặng tân thủ lên tới 10 triệu đồng trong tháng. Đặc biệt Megalive hàng tuần quà tặng lên tới 288 triệu.",
                                      created_at: new Date().toISOString(),
                                      user_id: "system",
                                      user: {
                                        full_name: "Tin nhắn hệ thống",
                                        avatar_url: "/icon/social.svg",
                                      },
                                    }
                                  : null
                              }
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === 2 && (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 min-h-0 overflow-y-auto">
                          <DirectChatTab
                            isLoggedIn={isLoggedIn}
                            onOpenAuthModal={openAuthModal}
                            matchId={matchId}
                            selectedRoomId={selectedRoomId}
                          />
                        </div>
                      </div>
                    )}

                    {activeTab === 3 && (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 min-h-0 overflow-y-auto">
                          <StatsTab
                            isLoading={!matchData}
                            matchData={matchData}
                          />
                        </div>
                      </div>
                    )}

                    {activeTab === 4 && (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 min-h-0 overflow-y-auto">
                          <CommentatorTab isLoading={!matchData} />
                        </div>
                      </div>
                    )}

                    {activeTab === 5 && (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 min-h-0 overflow-y-auto">
                          <LineupTab isLoading={!matchData} />
                        </div>
                      </div>
                    )}

                    {activeTab === 6 && (
                      /* Tỷ Lệ Tab */
                      <div className="h-full flex flex-col"></div>
                    )}

                    {activeTab === 7 && (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 min-h-0 overflow-y-auto">
                          <HeadToHeadTab isLoading={!matchData} />
                        </div>
                      </div>
                    )}

                    {activeTab === 8 && (
                      /* Thông Tin Tab */
                      <div className="h-full flex flex-col"></div>
                    )}

                    {activeTab === 9 && isMobile && (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 min-h-0 overflow-y-auto">
                          <RelatedMatchesTab
                            currentMatchId={matchId}
                            currentCategory={matchData?.category || "football"}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </aside>
            </div>
          </div>
          {/* RelatedMatchesTab - Ẩn trên mobile vì đã có trong tab */}
          <div className="hidden lg:block">
            <RelatedMatchesTab
              currentMatchId={matchId}
              currentCategory={matchData?.category || "football"}
            />
          </div>
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={closeAuthModal}
          initialMode={authMode}
          onModeChange={setAuthMode}
          onLoginSuccess={handleLoginSuccess}
        />

        {/* Floating Contact */}
        {/* <FloatingContact /> */}
      </div>
    </>
  );
}
