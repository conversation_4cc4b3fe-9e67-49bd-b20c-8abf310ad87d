"use client";

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import {
  ChatMessage as ChatMessageType
} from '@/services/chatService';
import type { ChatMessage as ChatMessageApi } from '@/types/chat.types';
import { useAuth } from '@/contexts/AuthContext';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import { useAutoMessages } from '@/hooks/useAutoMessages';

interface GeneralChatTabProps {
  isLoggedIn: boolean;
  onOpenAuthModal: (mode: 'login' | 'register') => void;
  matchId?: string;
  systemMessage?: {
    id: string;
    content: string;
    created_at: string;
    user_id: string;
    user: {
      full_name: string;
      avatar_url: string;
    };
  } | null;
}

export default function GeneralChatTab({ isLoggedIn, onOpenAuthModal, matchId = 'default', systemMessage }: GeneralChatTabProps) {
  const { user, chatRooms, chatRoomsLoading, messages, messagesLoading, loadChatRooms, reloadChatRooms, loadMessagesForRoom, sendMessage } = useAuth();
  
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [canLoadMore, setCanLoadMore] = useState(true);
  const [replyTo, setReplyTo] = useState<ChatMessageType | null>(null);

  // Local state for auto messages display
  const [autoMessages, setAutoMessages] = useState<ChatMessageType[]>([]);

  // Auto messages hook - only enable for general chat
  const { startAutoMessages, stopAutoMessages } = useAutoMessages({
    matchId,
    isEnabled: true,
    onAddMessage: (message: ChatMessageType) => {
      setAutoMessages(prev => [...prev, message]);
    }
  });

  // Mobile viewport handling
  const [viewportHeight, setViewportHeight] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isIOS, setIsIOS] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const [showFloatingButtons, setShowFloatingButtons] = useState(false);

  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Get user data from context or localStorage as fallback
  const getUserData = useCallback(() => {
    // First try to get from context
    if (user) {
      return {
        userId: user.id,
        name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
        email: user.email,
        verified: user.email_confirmed_at ? true : false,
        isAdmin: false // You can add admin logic here
      };
    }

    // Fallback to localStorage for backward compatibility
    const storedUser = localStorage.getItem('userData');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch (error) {
        // Error parsing user data
      }
    }
    return null;
  }, [user]);

  // Handle viewport and mobile detection
  const updateViewportInfo = useCallback(() => {
    const vh = window.innerHeight;
    const vw = window.innerWidth;
    setViewportHeight(vh);
    setIsMobile(vw < 1024);

    // Detect iOS
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    setIsIOS(isIOSDevice);

    // Detect keyboard height on mobile
    if (vw < 1024) {
      if (isIOSDevice) {
        // iOS: Use visual viewport to detect keyboard
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          const keyboardHeight = Math.max(0, window.innerHeight - visualViewport.height);
          setKeyboardHeight(keyboardHeight);
          setIsKeyboardOpen(keyboardHeight > 0);
        }
      } else {
        // Android: Use window height difference
        const initialHeight = window.visualViewport?.height || vh;
        const currentHeight = window.innerHeight;
        const keyboardHeight = Math.max(0, initialHeight - currentHeight);
        setKeyboardHeight(keyboardHeight);
        setIsKeyboardOpen(keyboardHeight > 0);
      }
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    // Chỉ scroll phần chat, không ảnh hưởng trang chính
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, []);

  // Load messages for the current match/room when chat rooms are available
  useEffect(() => {
    if (isLoggedIn && user?.id && !chatRoomsLoading) {
      // For general chat, use matchId
      loadMessagesForRoom(matchId);
    }
  }, [isLoggedIn, user?.id, chatRoomsLoading, matchId, loadMessagesForRoom]);

  // Initialize viewport info
  useEffect(() => {
    updateViewportInfo();
    window.addEventListener('resize', updateViewportInfo);
    window.addEventListener('orientationchange', updateViewportInfo);

    return () => {
      window.removeEventListener('resize', updateViewportInfo);
      window.removeEventListener('orientationchange', updateViewportInfo);
    };
  }, [updateViewportInfo]);

  // iOS: Handle keyboard events
  useEffect(() => {
    if (isIOS) {
      const handleIOSKeyboardShow = () => {
        setIsKeyboardOpen(true);
        setTimeout(updateViewportInfo, 100);
      };

      const handleIOSKeyboardHide = () => {
        setIsKeyboardOpen(false);
        setTimeout(updateViewportInfo, 100);
      };

      window.addEventListener('focusin', handleIOSKeyboardShow);
      window.addEventListener('focusout', handleIOSKeyboardHide);

      return () => {
        window.removeEventListener('focusin', handleIOSKeyboardShow);
        window.removeEventListener('focusout', handleIOSKeyboardHide);
      };
    }
  }, [isIOS, updateViewportInfo]);

  // Merge real messages and auto messages, then sort by timestamp
  const allMessages = React.useMemo(() => {
    // Convert real messages from API format to ChatMessageType format
    const realMessagesConverted = messages.map((msg: ChatMessageApi) => {
      const userData = getUserData();
      const isOwnMessage = userData?.userId === msg.user_id;

      return {
        id: msg.id,
        message: msg.content,
        timestamp: new Date(msg.created_at).getTime(),
        userId: msg.user_id,
        userName: isOwnMessage ? 'Bạn' : (msg.user?.full_name || 'Unknown User'),
        userAvatar: msg.user?.avatar_url || '',
        userColor: 'bg-blue-500',
        verified: false,
        isAdmin: false,
        replyTo: null,
        reactions: {} as { [key: string]: number },
        pinned: false,
        isAutoMessage: false
      } as ChatMessageType;
    });

    // Include auto messages for general chat
    const combined = [...realMessagesConverted, ...autoMessages];

    // Sort by timestamp (oldest first)
    return combined.sort((a, b) => a.timestamp - b.timestamp);
  }, [messages, autoMessages, getUserData]);

  // Auto-scroll to bottom when new messages arrive (chỉ scroll phần chat)
  useEffect(() => {
    if (allMessages.length > 0) {
      const timer = setTimeout(() => {
        // Chỉ scroll phần chat, không ảnh hưởng trang chính
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [allMessages.length]);

  // iOS: Scroll to bottom when keyboard opens
  useEffect(() => {
    if (isIOS && isKeyboardOpen && chatContainerRef.current) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isIOS, isKeyboardOpen]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const isScrolledUp = scrollTop < scrollHeight - clientHeight - 100;
      setShowScrollToBottom(isScrolledUp);

      // Check if we can load more messages
      if (scrollTop < 100 && canLoadMore && !isLoadingMore && messages.length > 0) {
        // loadOlderMessages();
      }
    }
  }, [canLoadMore, isLoadingMore, messages.length]);

  const handleSubmit = useCallback(async (messageText: string, replyToMessage?: ChatMessageType) => {
    if (!messageText.trim() || !isLoggedIn) return;

    const userData = getUserData();
    if (!userData) {
      onOpenAuthModal('login');
      return;
    }

    // Check if chat rooms are still loading
    if (chatRoomsLoading) {
      return;
    }

    // Check if we have any chat rooms
    if (chatRooms.length === 0) {
      await reloadChatRooms();
      return;
    }

    // For general chat, find by matchId or use first general room
    const currentRoom = chatRooms.find(room =>
      room.name === matchId ||
      room.type === matchId ||
      room.id === matchId
    ) || chatRooms.find(room => room.type === 'general') || chatRooms[0];

    if (!currentRoom) {
      // No room available to send message
      return;
    }

    try {
      const { error } = await sendMessage(currentRoom.id, messageText);
      if (error) {
        // Failed to send message
      } else {
        // Clear reply state after successful send
        setReplyTo(null);
      }
    } catch (error) {
      // Error sending message
    }
  }, [isLoggedIn, matchId, getUserData, onOpenAuthModal, chatRooms, chatRoomsLoading, reloadChatRooms, sendMessage]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // This will be handled by ChatInput component
    }
  }, []);

  const renderMessage = useCallback((message: ChatMessageType, index: number) => {
    const userData = getUserData();
    const isOwnMessage = userData?.userId === message.userId;

    return (
      <ChatMessage
        key={message.id}
        message={message}
        isOwnMessage={isOwnMessage}
        onReply={setReplyTo}
        onReact={() => {}}
        onPin={() => {}}
        onDelete={() => {}}
        isAdmin={false}
      />
    );
  }, [getUserData]);

  const getChatContainerStyle = () => {
    if (isMobile) {
      return {
        height: `calc(100vh - ${keyboardHeight}px - 200px)`,
        maxHeight: '65vh',
        minHeight: '300px',
      };
    }
    return {};
  };

  const getChatInputStyle = () => {
    if (isMobile) {
      return {
        paddingBottom: isKeyboardOpen ? '0px' : 'env(safe-area-inset-bottom, 0px)',
      };
    }
    return {};
  };

  const handleFloatingButtonClick = () => {
    setShowFloatingButtons(!showFloatingButtons);
  };

  const handleZaloClick = () => {
    window.open('https://zalo.me/**********', '_blank', 'noopener,noreferrer');
  };

  const handleTelegramClick = () => {
    window.open('https://t.me/thuphuongepl', '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      className="flex flex-col h-full relative"
      style={{
        paddingBottom: isMobile ? '0px' : 'env(safe-area-inset-bottom, 0px)',
        // Mobile: Ensure proper height and allow scrolling
        ...(isMobile ? {
          height: '100%',
          overflow: 'hidden',
        } : {})
      }}
    >
      {/* Messages Container */}
      <div
        className="flex-1 overflow-y-auto p-4 relative"
        ref={chatContainerRef}
        onScroll={handleScroll}
        style={getChatContainerStyle()}
      >
        {/* Loading indicator for older messages */}
        {isLoadingMore && (
          <div className="text-center py-2">
            <div className="inline-flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              <span>Đang tải tin nhắn cũ...</span>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className={`space-y-2 ${isMobile ? 'pb-36' : ''}`}>
          {/* All messages (real + auto) sorted by timestamp */}
          {allMessages.map(renderMessage)}
        </div>

        {/* Login to comment card */}
        {!isLoggedIn && (
          <div
            className="mt-4 p-4 bg-white dark:bg-custom-dark border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm cursor-pointer relative z-20 hover:bg-white dark:hover:bg-gray-700 transition-colors"
            onClick={() => onOpenAuthModal('login')}
            style={{
              // Ensure it's always visible on mobile
              ...(isMobile ? {
                position: 'relative',
                zIndex: 20,
                marginBottom: '80px' // Add space for ChatInput
              } : {
                marginTop: '150px'
              })
            }}
          >
            <div className="text-center">
              <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">Đăng nhập để bình luận</p>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div
        className={isMobile ? "fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-custom-dark border-t border-gray-200 dark:border-gray-700" : "mb-5 md:mb-0"}
        style={getChatInputStyle()}
      >
        <ChatInput
          onSubmit={handleSubmit}
          isLoggedIn={isLoggedIn}
          replyTo={replyTo}
          onCancelReply={() => setReplyTo(null)}
          placeholder="Nhập tin nhắn..."
          disabled={false}
          noBorderRadius={isMobile}
          onOpenAuthModal={onOpenAuthModal}
        />
      </div>
      
      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <div
          className="absolute right-4 z-20"
          style={{
            bottom: isMobile ? '80px' : '70px'
          }}
        >
          <button
            onClick={scrollToBottom}
            className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white shadow-lg hover:bg-blue-600 transition-all duration-200 hover:scale-105"
            title="Scroll xuống tin nhắn mới nhất"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </button>
        </div>
      )}

      {/* Floating Action Button - Above ChatInput on the right */}
      <div className="absolute bottom-20 right-5 z-50">
        {/* Main floating button */}
        <button
          onClick={handleFloatingButtonClick}
          className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          title="Liên hệ hỗ trợ"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </button>

        {/* Zalo and Telegram buttons */}
        {showFloatingButtons && (
          <div className="absolute bottom-14 right-2 flex flex-col space-y-2">
            {/* Zalo button */}
            <button
              onClick={handleZaloClick}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-500 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              title="Liên hệ Zalo"
            >
              <div className="w-5 h-5 flex items-center justify-center font-bold text-sm">
                Z
              </div>
            </button>

            {/* Telegram button */}
            <button
              onClick={handleTelegramClick}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-400 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              title="Liên hệ Telegram"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
