# Google Sheets Mapping với fetchMatches API

Hướng dẫn map dữ liệu từ Google Sheets với API `fetchMatches` để hiển thị viền đỏ cho các trận đấu khớp yêu cầu.

## 📊 C<PERSON>u trúc Google Sheets

Bảng Google Sheets cần có cấu trúc như sau:

| A (order) | B (id) | C (name_explanation) | D (home_team) | E (away_team) | F (team_name_and_time) | G (time) |
|-----------|--------|---------------------|---------------|---------------|------------------------|----------|
| 1         | GBAd37s | ...                | ...           | ...           | ...                    | ...      |
| 2         | o7pEYXa | ...                | ...           | ...           | ...                    | ...      |
|           | 3Q7hI5  | ...                | ...           | ...           | ...                    | ...      |
| 3         | 4wq2ghnxp5zm0v | ...         | ...           | ...           | ...                    | ...      |

### Các cột quan trọng:
- **Cột A (order)**: Giá trị order (có thể để trống)
- **Cột B (id)**: Match ID để map với `match.id` từ API

## 🎯 Logic hiển thị viền

### 🔴 Viền đỏ (Red Border)
**Điều kiện**: `match.status === 'live' && order !== ''`
- Match phải có status = "live"
- Match ID phải có trong Google Sheets
- Cột order phải có giá trị (không rỗng)

### 🟠 Viền cam (Orange Border)  
**Điều kiện**: `match.status === 'live' && order === ''`
- Match có status = "live"
- Nhưng không có order hoặc không có trong Google Sheets

### ⚪ Viền thường (Normal Border)
**Điều kiện**: `match.status !== 'live'`
- Match không phải live

## 🚀 Cách sử dụng

### 1. Sử dụng Hook tự động

```tsx
import { useAutoMatchOrderMapper } from '@/hooks/useMatchOrderMapper';
import { fetchMatches } from '@/services/matchService';

function MyComponent() {
  const [matches, setMatches] = useState([]);
  
  // Fetch matches từ API
  useEffect(() => {
    const loadMatches = async () => {
      const result = await fetchMatches({ limit: 20 });
      setMatches(result.data);
    };
    loadMatches();
  }, []);

  // Auto map với Google Sheets
  const { mappedMatches, loading, error, stats } = useAutoMatchOrderMapper(
    matches, 
    'YOUR_SPREADSHEET_ID',
    'Sheet1',  // Tên sheet
    'A1:G'    // Range A-G
  );

  return (
    <div>
      {mappedMatches.map(match => (
        <MatchCard key={match.id} match={match} />
      ))}
    </div>
  );
}
```

### 2. Sử dụng Manual mapping

```tsx
import { useMatchOrderMapper } from '@/hooks/useMatchOrderMapper';

function MyComponent() {
  const { mappedMatches, loading, mapMatches } = useMatchOrderMapper();
  
  const handleMapData = async () => {
    const result = await fetchMatches({ limit: 20 });
    await mapMatches(
      result.data, 
      'YOUR_SPREADSHEET_ID',
      'Sheet1',
      'A1:G'
    );
  };

  return (
    <div>
      <button onClick={handleMapData}>Map Data</button>
      {mappedMatches.map(match => (
        <MatchCard key={match.id} match={match} />
      ))}
    </div>
  );
}
```

### 3. Sử dụng Utility functions

```tsx
import { fetchAndMapMatchesWithOrder } from '@/utils/matchOrderMapper';

async function loadMappedData() {
  // 1. Fetch matches
  const { data: matches } = await fetchMatches({ limit: 20 });
  
  // 2. Map với Google Sheets
  const mappedMatches = await fetchAndMapMatchesWithOrder(
    matches,
    'YOUR_SPREADSHEET_ID',
    'Sheet1',
    'A1:G'
  );
  
  // 3. Filter matches có red border
  const redBorderMatches = mappedMatches.filter(m => m.orderInfo?.hasRedBorder);
  
  return { mappedMatches, redBorderMatches };
}
```

## 📁 Files được tạo

### Core Files:
- `src/utils/matchOrderMapper.ts` - Core mapping logic
- `src/hooks/useMatchOrderMapper.ts` - React hooks
- `src/components/examples/MatchOrderDemo.tsx` - Demo component

### Updated Files:
- `src/components/common/MatchCard.tsx` - Updated để support red border

## 🔧 Configuration

```tsx
// Cấu hình Google Sheets
const SPREADSHEET_ID = 'YOUR_SPREADSHEET_ID';
const SHEET_NAME = 'Sheet1';  // Tên sheet của bạn
const RANGE = 'A1:G';         // Range A-G như trong hình

// Sử dụng
const { mappedMatches } = useAutoMatchOrderMapper(
  matches, 
  SPREADSHEET_ID, 
  SHEET_NAME, 
  RANGE
);
```

## 📊 Data Structure

### Input từ fetchMatches API:
```typescript
interface MatchData {
  id: string;           // Match ID để map
  status: string;       // "live", "finished", etc.
  homeTeam: {...};
  awayTeam: {...};
  // ... other fields
}
```

### Input từ Google Sheets:
```typescript
interface GoogleSheetRowData {
  order: string;              // Cột A
  id: string;                 // Cột B - Match ID
  name_explanation?: string;  // Cột C
  home_team?: string;         // Cột D
  away_team?: string;         // Cột E
  team_name_and_time?: string; // Cột F
  time?: string;              // Cột G
}
```

### Output sau khi map:
```typescript
interface MatchWithOrderInfo extends MatchData {
  orderInfo?: {
    order: string;           // Giá trị từ cột A
    hasRedBorder: boolean;   // true nếu cần red border
    isFromSheet: boolean;    // true nếu có trong Sheets
    sheetData?: GoogleSheetRowData; // Raw data từ Sheets
  };
}
```

## 🎨 CSS Classes

### Red Border:
```css
border-2 border-red-500 shadow-lg shadow-red-500/50 live-glow-animation
```

### Orange Border:
```css
border-2 border-orange-500 shadow-lg shadow-orange-500/50 live-glow-animation
```

### Normal Border:
```css
border border-gray-200 dark:border-custom-dark-secondary
```

## 🧪 Testing

Sử dụng component demo để test:

```tsx
import MatchOrderDemo from '@/components/examples/MatchOrderDemo';

function TestPage() {
  return <MatchOrderDemo />;
}
```

## 🔍 Debug

Hook cung cấp stats để debug:

```tsx
const { mappedMatches, stats } = useAutoMatchOrderMapper(...);

console.log('Stats:', {
  total: stats.total,           // Tổng số matches
  inSheet: stats.inSheet,       // Số matches có trong Sheets
  withRedBorder: stats.withRedBorder, // Số matches có red border
  liveMatches: stats.liveMatches      // Số live matches
});
```

## ⚠️ Lưu ý

1. **Spreadsheet ID**: Thay `YOUR_SPREADSHEET_ID` bằng ID thực của Google Sheets
2. **Sheet Name**: Đảm bảo tên sheet đúng (mặc định: 'Sheet1')
3. **Range**: Adjust range nếu có thêm cột (mặc định: 'A1:G')
4. **API Permissions**: Đảm bảo Google Sheets API có quyền truy cập
5. **Match ID**: ID trong Sheets phải khớp chính xác với `match.id` từ API
