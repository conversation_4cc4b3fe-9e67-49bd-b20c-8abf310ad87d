import { MatchData } from '@/types/match';

// Interface cho dữ liệu từ Google Sheets theo c<PERSON>u trúc của bạn
export interface GoogleSheetRowData {
  order: string;              // Cột A - order
  id: string;                 // Cột B - id (match ID)
  name_explanation?: string;  // Cột C - name_explanation
  home_team?: string;         // Cột D - home_team
  away_team?: string;         // Cột E - away_team
  team_name_and_time?: string; // Cột F - team_name_and_time
  time?: string;              // Cột G - time
}

// Interface cho match đã được map
export interface MatchWithOrderInfo extends MatchData {
  orderInfo?: {
    order: string;
    hasRedBorder: boolean;    // true nếu cần hiển thị viền đỏ
    isFromSheet: boolean;     // true nếu có trong Google Sheets
    sheetData?: GoogleSheetRowData; // Dữ liệu gốc từ sheet
  };
}

/**
 * Fetch dữ liệu từ Google Sheets
 */
export async function fetchOrderSheetData(
  spreadsheetId: string, 
  sheetName: string = 'Sheet1', 
  range: string = 'A1:G'
): Promise<GoogleSheetRowData[]> {
  try {
    const url = `/api/google-sheets?spreadsheetId=${encodeURIComponent(spreadsheetId)}&range=${encodeURIComponent(`${sheetName}!${range}`)}`;
    const response = await fetch(url);
    const result = await response.json();
    
    if (!result.success || !result.data?.values) {
      throw new Error(`Failed to fetch Google Sheets data: ${result.error || 'Unknown error'}`);
    }
    
    // Convert array data to object format
    const values = result.data.values as string[][];
    const rows = values.slice(1); // Skip header row
    
    const formattedData: GoogleSheetRowData[] = rows.map(row => ({
      order: row[0] || '',              // Cột A
      id: row[1] || '',                 // Cột B
      name_explanation: row[2] || '',   // Cột C
      home_team: row[3] || '',          // Cột D
      away_team: row[4] || '',          // Cột E
      team_name_and_time: row[5] || '', // Cột F
      time: row[6] || ''                // Cột G
    }));
    
    console.log('✅ Fetched Google Sheets data:', formattedData.length, 'rows');
    return formattedData;
  } catch (error) {
    console.error('❌ Error fetching Google Sheets:', error);
    throw error;
  }
}

/**
 * Tạo Map để lookup nhanh dữ liệu từ Google Sheets theo ID
 */
export function createOrderDataMap(sheetData: GoogleSheetRowData[]): Map<string, GoogleSheetRowData> {
  const map = new Map<string, GoogleSheetRowData>();

  sheetData.forEach((row, index) => {
    const matchId = row.id?.trim();
    if (matchId && matchId !== '') {
      map.set(matchId, row);
      console.log(`📋 Sheet row ${index + 1}: ID="${matchId}", Order="${row.order}"`);
    } else {
      console.log(`⚠️ Sheet row ${index + 1}: Missing or empty ID`);
    }
  });

  console.log('📊 Created order data map with', map.size, 'entries');
  console.log('📋 Sheet IDs:', Array.from(map.keys()));
  return map;
}

/**
 * Kiểm tra điều kiện để hiển thị viền đỏ
 * Điều kiện: match.status === 'live' && order !== ''
 */
export function shouldShowRedBorder(match: MatchData, sheetRow: GoogleSheetRowData): boolean {
  const orderValue = sheetRow.order?.trim() || '';
  const hasOrder = orderValue !== '';
  const isLive = match.status === 'live';

  console.log(`🔍 Red border check for ${match.id}: live=${isLive}, order="${orderValue}", hasOrder=${hasOrder}`);

  return isLive && hasOrder;
}

/**
 * Map matches với dữ liệu từ Google Sheets
 */
export function mapMatchesWithOrderData(
  matches: MatchData[],
  sheetData: GoogleSheetRowData[]
): MatchWithOrderInfo[] {
  console.log(`🔄 Mapping ${matches.length} matches with ${sheetData.length} sheet rows`);
  
  // Tạo Map để lookup nhanh
  const orderDataMap = createOrderDataMap(sheetData);
  
  // Debug: Log all match IDs
  console.log('🔍 Match IDs to map:', matches.map(m => m.id));
  console.log('🔍 Available sheet IDs:', Array.from(orderDataMap.keys()));

  // Map từng match
  const mappedMatches: MatchWithOrderInfo[] = matches.map(match => {
    console.log(`🔄 Processing match: ${match.id} (status: ${match.status})`);

    const sheetRow = orderDataMap.get(match.id);

    if (sheetRow) {
      // Có dữ liệu từ Google Sheets - CHÍNH XÁC KHỚP ID
      const hasRedBorder = shouldShowRedBorder(match, sheetRow);

      console.log(`✅ EXACT MATCH found for ${match.id}: order="${sheetRow.order}", status="${match.status}", redBorder=${hasRedBorder}`);

      return {
        ...match,
        orderInfo: {
          order: sheetRow.order || '',
          hasRedBorder,
          isFromSheet: true,
          sheetData: sheetRow
        }
      };
    } else {
      // Không có trong Google Sheets - KHÔNG KHỚP ID
      console.log(`❌ NO MATCH found for ${match.id}: not in sheet, keeping normal border`);

      return {
        ...match,
        orderInfo: {
          order: '',
          hasRedBorder: false,
          isFromSheet: false
        }
      };
    }
  });

  const stats = {
    total: mappedMatches.length,
    inSheet: mappedMatches.filter(m => m.orderInfo?.isFromSheet).length,
    withRedBorder: mappedMatches.filter(m => m.orderInfo?.hasRedBorder).length,
    liveMatches: mappedMatches.filter(m => m.status === 'live').length
  };

  console.log('🎯 Mapping stats:', stats);
  
  return mappedMatches;
}

/**
 * Main function để fetch và map tất cả dữ liệu
 */
export async function fetchAndMapMatchesWithOrder(
  matches: MatchData[],
  spreadsheetId: string,
  sheetName: string = 'Sheet1',
  range: string = 'A1:G'
): Promise<MatchWithOrderInfo[]> {
  try {
    console.log('🚀 Starting fetch and map process...');
    
    // Fetch Google Sheets data
    const sheetData = await fetchOrderSheetData(spreadsheetId, sheetName, range);
    
    // Map với matches
    const mappedMatches = mapMatchesWithOrderData(matches, sheetData);
    
    console.log('✅ Fetch and map completed successfully!');
    return mappedMatches;
  } catch (error) {
    console.error('❌ Error in fetchAndMapMatchesWithOrder:', error);
    throw error;
  }
}

/**
 * Utility function để filter matches theo điều kiện
 */
export function filterMatchesByCondition(
  mappedMatches: MatchWithOrderInfo[],
  condition: {
    hasRedBorder?: boolean;
    isFromSheet?: boolean;
    hasOrder?: boolean;
    status?: string;
  }
): MatchWithOrderInfo[] {
  return mappedMatches.filter(match => {
    if (condition.hasRedBorder !== undefined && !!match.orderInfo?.hasRedBorder !== condition.hasRedBorder) {
      return false;
    }
    
    if (condition.isFromSheet !== undefined && !!match.orderInfo?.isFromSheet !== condition.isFromSheet) {
      return false;
    }
    
    if (condition.hasOrder !== undefined && !!(match.orderInfo?.order) !== condition.hasOrder) {
      return false;
    }
    
    if (condition.status && match.status !== condition.status) {
      return false;
    }
    
    return true;
  });
}

/**
 * Utility function để sort matches theo order
 */
export function sortMatchesByOrder(mappedMatches: MatchWithOrderInfo[]): MatchWithOrderInfo[] {
  return [...mappedMatches].sort((a, b) => {
    const orderA = a.orderInfo?.order || '';
    const orderB = b.orderInfo?.order || '';
    
    // Matches có order lên trước
    if (orderA && !orderB) return -1;
    if (!orderA && orderB) return 1;
    
    // Nếu cả 2 đều có order, sort theo số
    if (orderA && orderB) {
      const numA = parseInt(orderA) || 0;
      const numB = parseInt(orderB) || 0;
      return numA - numB;
    }
    
    // Nếu cả 2 đều không có order, sort theo status (live trước)
    if (a.status === 'live' && b.status !== 'live') return -1;
    if (a.status !== 'live' && b.status === 'live') return 1;
    
    return 0;
  });
}
